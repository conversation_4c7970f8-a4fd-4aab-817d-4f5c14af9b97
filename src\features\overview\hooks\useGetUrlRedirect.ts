import { useAuthStore } from "@/features/auth/slices/authStore";
import { AccountTypes } from "@/types/globalType";
import { storeShortcutItems, adminStoreShortcutItems } from "@/features/overview/constants";

export const useGetUrlRedirect = () => {
  const { user, typeStore } = useAuthStore();

  const getShortcutItem = () => {
    if (user?.statusAccount === AccountTypes.ADMIN_STORE) {
      return adminStoreShortcutItems(typeStore);
    }

    return storeShortcutItems(typeStore);
  }

  return {
    merchantNo: user?.agxMerchantNo,
    storeName: user?.agxStoreName,
    accountType: user?.statusAccount,
    shortcutItems: getShortcutItem()
  }
}