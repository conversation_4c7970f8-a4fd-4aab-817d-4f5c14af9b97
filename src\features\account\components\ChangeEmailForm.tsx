import React from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { useAuthStore } from "@/store";
import { useChangeInfo } from "../hooks/useChangeInfo";
import { emailSchema } from "../schema";
import { EditMode, EmailFormData } from "../types";

interface FormProps {
  editMode: EditMode;
  setEditMode: (mode: EditMode) => void;
}

function ChangeEmailForm({ editMode, setEditMode }: FormProps) {
  const emailForm = useForm<EmailFormData>({
    resolver: zodResolver(emailSchema),
    defaultValues: { newEmail: "", confirmEmail: "" },
  });
  const { user } = useAuthStore();
  const { changeInfo } = useChangeInfo();

  const handleSubmit = emailForm.handleSubmit(async (data) => {
    await changeInfo({
      contactId: user?.id,
      firstName: user?.firstName,
      lastName: user?.lastName,
      email: data.newEmail,
    });
    setEditMode(EditMode.None);
    // clear form data
    emailForm.reset({
      newEmail: data.newEmail,
      confirmEmail: data.confirmEmail,
    });
  });

  return (
    <Card className="rounded-none border-t-0 border-r-0 border-l-0 shadow-none border-b-2 border-gray-400">
      <form
        onSubmit={handleSubmit}
        className="space-y-4 text-[#6F6F6E] font-semibold"
      >
        <CardContent className="px-0">
          {editMode === "email" ? (
            <>
              <div>
                <Label className="flex items-center justify-between text-[#6F6F6E] text-xl font-semibold mb-1">
                  ログインメールアドレス
                </Label>
                <div className="text-xl">{user.email}</div>
              </div>
              <div className="space-y-6 mt-5 sm:mt-10">
                <div className="flex flex-col sm:grid sm:grid-cols-[auto_1fr] gap-x-8 gap-y-4 sm:gap-y-8 sm:items-center ml-2 lg:ml-8">
                  <Label
                    htmlFor="newEmail"
                    className="text-lg whitespace-nowrap text-left md:text-left font-semibold"
                  >
                    新しいメールアドレス
                  </Label>
                  <div>
                    <Input
                      id="newEmail"
                      {...emailForm.register("newEmail")}
                      className="rounded-[8px] h-[40px] text-base w-full max-w-[400px] border border-[#BABABA]"
                    />
                    {emailForm.formState.errors.newEmail && (
                      <p className="text-red-500 text-sm mt-1">
                        {emailForm.formState.errors.newEmail.message}
                      </p>
                    )}
                  </div>

                  <Label
                    htmlFor="confirmEmail"
                    className="text-lg whitespace-nowrap text-left md:text-left font-semibold"
                  >
                    新しいメールアドレス（確認）
                  </Label>
                  <div className="flex items-center gap-4">
                    <div className="flex-1">
                      <Input
                        id="confirmEmail"
                        {...emailForm.register("confirmEmail")}
                        className="rounded-[8px] h-[40px] text-base w-full max-w-[400px] border border-[#BABABA]"
                      />
                      {emailForm.formState.errors.confirmEmail && (
                        <p className="text-red-500 text-sm mt-1">
                          {emailForm.formState.errors.confirmEmail.message}
                        </p>
                      )}
                    </div>
                    <Button
                      type="submit"
                      className="bg-teal-600 hover:bg-teal-700 text-white px-8 w-[196px] h-[50px] text-xl rounded-[8px] xl:block hidden"
                    >
                      保存
                    </Button>
                  </div>

                  <div className="col-span-2 text-[#6F6F6E]/80 text-base">
                    他のアカウントで使用済みのメールアドレスは設定できません
                  </div>
                </div>
                <Button
                  type="submit"
                  className="mx-auto bg-teal-600 hover:bg-teal-700 text-white px-8 w-[196px] h-[50px] text-xl rounded-[8px] xl:hidden block"
                >
                  保存
                </Button>
              </div>
            </>
          ) : (
            <div className="flex gap-4 items-center justify-between text-[#6F6F6E]">
              <div>
                <Label className="font-semibold text-[#6F6F6E] text-xl">
                  ログインメールアドレス
                </Label>
                <div className="text-base sm:text-xl">{user?.email}</div>
              </div>
              <Button
                variant="secondary"
                onClick={() => setEditMode(EditMode.Email)}
                className="bg-[#F7F7F7] hover:bg-[#CCCCCC] text-gray-700 w-[120px] md:w-[196px] h-[50px] text-base md:text-xl"
              >
                編集
              </Button>
            </div>
          )}
        </CardContent>
      </form>
    </Card>
  );
}

export default ChangeEmailForm;
