import { useState, useMemo } from 'react';
import { useQuery, useMutation } from '@tanstack/react-query';
import { useAuthStore } from '@/store';
import { invoiceService } from '../services/invoiceService';

// Interface cho state dữ liệu
interface AgxInvoice {
  year?: number;
  month?: number;
  total?: number;
  yearMonth?: string;
  invoiceUrl?: string;
  receiptUrl?: string;
  deadlineDate?: string;
}

interface AgxInvoiceDetail {
  deadlineDate: string;
  invoiceStatus: number;
  invoiceUrl: string;
  receiptUrl: string;
  total: number;
  agxStatus?: number;
}

interface GmoData {
  memberId: string | null;
  cardNo: string | null;
}

// Initial states
const initialAgxInvoiceDetail: AgxInvoiceDetail = {
  deadlineDate: "",
  invoiceStatus: 0,
  invoiceUrl: "",
  receiptUrl: "",
  total: 0
};

const initialGmoData: GmoData = {
  memberId: null,
  cardNo: null
};

export const useStoreInvoice = (agxMerchantNoGetInvoice?: string) => {
  const { user } = useAuthStore();
  const [open, setOpen] = useState(false);
  
  const agxMerchantNo = user?.agxMerchantNo || ''

  // Query for getting AGX invoice list
  const {
    data: agxInvoice = [],
    isLoading: agxInvoiceLoading,
    error: agxInvoiceError
  } = useQuery({
    queryKey: ['agx-invoice', agxMerchantNoGetInvoice],
    queryFn: () => invoiceService.getAgxInvoice(btoa(agxMerchantNoGetInvoice)),
    enabled: !!agxMerchantNoGetInvoice,
    select: (response) => response.data,
  });

  // Query for getting AGX invoice detail
  const {
    data: agxInvoiceDetail = initialAgxInvoiceDetail,
    isLoading: agxInvoiceDetailLoading,
    error: agxInvoiceDetailError
  } = useQuery({
    queryKey: ['agx-invoice-detail', agxMerchantNoGetInvoice],
    queryFn: () => invoiceService.getAgxInvoiceDetail(btoa(agxMerchantNoGetInvoice)),
    enabled: !!agxMerchantNoGetInvoice,
    select: (response) => response.data,
  });

  // Query for getting GMO member info
  const {
    data: gmoData = initialGmoData,
    isLoading: gmoDataLoading,
    error: gmoDataError
  } = useQuery({
    queryKey: ['gmo-info', agxMerchantNo],
    queryFn: () => invoiceService.getGmoInfo(btoa(agxMerchantNo)),
    enabled: !!agxMerchantNo,
    select: (response) => response.data,
  });

  // Mutation for GMO payment
  const gmoPaymentMutation = useMutation({
    mutationFn: () => invoiceService.getGmoPaymentUrl(btoa(agxMerchantNo)),
    onSuccess: (response) => {
      if (response.data?.LinkUrl) {
        window.open(response.data.LinkUrl);
      }
    },
    onError: (error) => {
      console.error('Error handling GMO payment:', error);
    },
  });

  // Mutation for creating linkplus URL
  const linkplusUrlMutation = useMutation({
    mutationFn: ({ memberId, cardNo }: { memberId: string; cardNo: string }) => 
      invoiceService.getGmoLinkplusUrl(memberId, cardNo),
    onSuccess: (response) => {
      if (response.data?.LinkUrl) {
        window.open(response.data.LinkUrl);
      }
    },
    onError: (error) => {
      console.error('Error creating linkplus URL:', error);
    },
  });

  const handlePaymentGMO = () => {
    if (!gmoData?.cardNo && agxMerchantNo) {
      gmoPaymentMutation.mutate();
    }
  };

  const handleCreateLinkplusUrl = () => {
    window.open(
      'https://stg.link.mul-pay.jp/v2/plus/tshop00054655/member/39adc5bd1fba451f18e8e4324bf644cf335bbb4043daf434d23690bc9d3276f6',
      '_blank'
    )
  };

  const handleUsageDetails = () => {
    window.open(
      'https://stg.link.mul-pay.jp/v2/plus/tshop00054655/checkout/d575d4a58c533a0550b72de9fbf904ef40feb3ecbfcd5af48208b749611defcc',
      '_blank'
    )
    setOpen(false);
  };

  // Memo để tối ưu hiệu năng
  const infoGMO = useMemo(() => ({ ...gmoData }), [gmoData]);

  // Combine loading states
  const loading = agxInvoiceLoading || agxInvoiceDetailLoading || gmoDataLoading;

  // Combine errors (prioritize important errors)
  const error = agxInvoiceError?.message || agxInvoiceDetailError?.message || null;

  // Utility functions - using centralized formatter
  // formatNumber is imported from utils/formatters

  const getUrlBreadcrumbInvoice = () => {
    return `/store`;
  };

  return {
    open,
    setOpen,

    // Data
    agxInvoice,
    agxInvoiceDetail,
    gmoData,
    loading,
    error,
    
    // Actions
    handlePaymentGMO,
    handleCreateLinkplusUrl,
    handleUsageDetails,
    
    // Mutation states
    isPaymentLoading: gmoPaymentMutation.isPending,
    isLinkplusLoading: linkplusUrlMutation.isPending,
    paymentError: gmoPaymentMutation.error,
    linkplusError: linkplusUrlMutation.error,
    
    // Utilities
    getUrlBreadcrumbInvoice
  };
};
