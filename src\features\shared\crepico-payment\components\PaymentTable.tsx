import React from 'react';
import { ChevronDown, ChevronUp, Download } from 'lucide-react';
import { CSVLink } from 'react-csv';
import { PaymentsResponse, SortDirection, PaymentData } from '../types';
import { formatMoney, formatNumber } from '@/utils/dateUtils';
import {
    mapTransactionCodePaymentData,
    mapTransactionTypeCredit,
    mapTransactionTypeOther
} from '@/constants/common.constant';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';

interface PaymentTableProps {
    payments: PaymentsResponse | null;
    page: number;
    setPage: (page: number) => void;
    sortDirectionByValue: SortDirection;
    handleSort: (key: keyof SortDirection) => void;
    handleExportData: () => void;
    csvExport: {
        data: any[];
        headers: any[];
        filename: string;
        separator: string;
        enclosingCharacter: string;
    };
    csvLinkRef: React.RefObject<any>;
}


export const PaymentTable: React.FC<PaymentTableProps> = ({
    payments,
    page,
    setPage,
    sortDirectionByValue,
    handleSort,
    handleExportData,
    csvExport,
    csvLinkRef
}) => {
    const tableMaxWidth = useDynamicTableWidth();
    const SortButton = ({ field, children }) => (
        <button
            className="flex items-center justify-center w-full transition-colors hover:bg-white"
            onClick={() => handleSort(field)}
        >
            {children}
            {sortDirectionByValue[field] === null
                ? null
                : sortDirectionByValue[field]
                    ? <ChevronDown className="ml-1 w-3 h-3" />
                    : <ChevronUp className="ml-1 w-3 h-3" />}
        </button>
    );
    return (
        <div className='bg-white rounded-lg shadow-sm mt-20'>
            <div className='flex justify-between items-center w-full mb-6 md:flex-row flex-col gap-4'>
                <div className='flex'>
                    <h2 className='text-2xl font-semibold'>
                        合計{payments ? formatNumber(payments?.totalYen) : 0}円「{payments ? payments?.totalItem : 0}件」
                    </h2>
                </div>

                {/* Pagination */}
                <div className='flex items-center gap-4'>
                    {payments && payments?.data?.totalPages && (
                        <nav className='flex items-center'>
                            <div className="flex items-center gap-6">
                                {Array.from(Array(Math.min(payments?.data?.totalPages, 6)).keys()).map(i => {
                                    const isCurrentPage = i + 1 === page + 1;

                                    return (
                                        <button
                                            key={i}
                                            onClick={() => setPage(i)}
                                            className={`text-2xl font-semibold transition-colors duration-200 pb-1 ${isCurrentPage
                                                ? 'text-[#1D9987] border-b-2 border-[#1D9987]'
                                                : 'text-[#6F6F6E] hover:text-[#6F6F6E]/80'
                                                }`}
                                        >
                                            {i + 1}
                                        </button>
                                    );
                                })}
                            </div>
                        </nav>
                    )}
                </div>

                {/* Export Button */}
                <div className="flex items-center gap-2 p-2 text-[#1D9987] rounded">
                    <Download
                        className="w-8 h-8 hover:text-[#1D9987]/80 hover:opacity-80 cursor-pointer transition-colors duration-200"
                        onClick={handleExportData}
                    />
                    <span className='text-[#6F6F6E]'>CSV Download</span>
                </div>

                {/* Hidden CSVLink for actual download */}
                {/* @ts-expect-error : type mismatch due to version node */}
                <CSVLink
                    ref={csvLinkRef}
                    data={csvExport.data}
                    headers={csvExport.headers}
                    filename={csvExport.filename}
                    separator={csvExport.separator}
                    enclosingCharacter={csvExport.enclosingCharacter}
                    style={{ display: 'none' }}
                />
            </div>

            {/* Table */}
            <div className="bg-white w-full overflow-x-auto" style={{ maxWidth: tableMaxWidth }}>
                <Table className='text-xl'>
                    <TableHeader>
                        <TableRow className="border-none hover:bg-white">
                            <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[140px] w-1/6 text-[#6F6F6E] font-medium">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-2/3' >
                                        <SortButton field="terminalHandlingSerialNumber">
                                            伝票番号
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                            <TableHead className="text-center py-4 px-1 xl:px-6 w-1/6 text-[#6F6F6E] font-medium">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-2/3'>
                                        <SortButton field="saleAmount">
                                            売上金額
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                            <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[120px] w-1/6 text-[#6F6F6E] font-medium ">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-2/3'>
                                        <SortButton field="transactionCode">
                                            決済状況
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                            <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[180px] w-1/6 text-[#6F6F6E] font-medium ">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-1/2'>
                                        <SortButton field="terminalProcessingDate">
                                            決済日時
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                            <TableHead className="text-center py-4 px-1 xl:px-6 w-1/6 text-[#6F6F6E] font-medium ">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-2/3'>
                                        <SortButton field="terminalIdentificationNumber">
                                            端末識別番号
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                            <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[180px] w-1/6 text-[#6F6F6E] font-medium ">
                                <div className='w-full flex items-center justify-center'>
                                    <div className='flex items-center justify-center py-4 border-b border-gray-400 w-full xl:w-1/2'>
                                        <SortButton field="serviceIdentificationFlag">
                                            決済種別
                                        </SortButton>
                                    </div>
                                </div>
                            </TableHead>
                        </TableRow>
                    </TableHeader>
                    <TableBody>
                        {payments?.data?.data && payments.data.data.map((p, i) => (
                            <TableRow key={i} className={`${i % 2 === 1 ? 'bg-gray-50 hover:bg-gray-50' : 'bg-white hover:bg-white'} border-b-0 text-[#6F6F6E]`}>
                                <TableCell className="text-center py-4 px-6">
                                    {p.terminalHandlingSerialNumber}
                                </TableCell>
                                <TableCell className="text-center py-4 px-6">
                                    ¥{Number(p.saleAmount).toLocaleString()}
                                </TableCell>
                                <TableCell className="text-center py-4 px-6">
                                    {mapTransactionCodePaymentData.get(p.transactionCode)}
                                </TableCell>
                                <TableCell className="text-center py-4 px-6">
                                    {p.terminalProcessingDate}
                                </TableCell>
                                <TableCell className="text-center py-4 px-6">
                                    {p.terminalIdentificationNumber}
                                </TableCell>
                                <TableCell className="text-center py-4 px-6">
                                    {p.serviceIdentificationFlag === '1'
                                        ? mapTransactionTypeCredit.get(p.transactionCategory)
                                        : mapTransactionTypeOther.get(p.serviceIdentificationFlag)}
                                </TableCell>
                            </TableRow>
                        ))}
                    </TableBody>
                </Table>
            </div>
        </div>
    );
};
