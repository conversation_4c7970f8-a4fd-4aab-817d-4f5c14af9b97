import { TypeStore } from "@/types/globalType";
import HamburgerIcon from "@/assets/images/グループ 1345.svg";
import ScrollIcon from "@/assets/images/グループ 1346.svg";
import BookIcon from "@/assets/images/グループ 1347.svg";
import ScreenIcon from "@/assets/images/グループ 1348.svg";
import QuestionIcon from "@/assets/images/グループ 1393.svg";
import MailIcon from "@/assets/images/グループ 1389.svg";
import SettingsIcon from '@/assets/images/Icon settings.svg';
import ReceiptIcon from '@/assets/images/Icon payment-invoice-sign-alt-o.svg';
import { getStoreNotificationFaqUrl, getStoreNotificationRollPaperUrl } from "@/features/app-sidebar/constants/sidebarConstants";

export interface ShortcutItem {
  title: string;
  url: string;
  icon: string;
  isExternal?: boolean,
  isModal?: boolean,
  iconClass?: string
}

const getUrlDeposit = (typeStore: TypeStore) => {
  if (typeStore === TypeStore.STORE_CREPICO) {
    return "/store/deposit/crepico";
  }
  if (typeStore === TypeStore.STORE_PAYGATE) {
    return "/store/deposit/paygate";
  }
  return "/store/deposit";
}

const getUrlInvoiceReceipt = (typeStore: TypeStore) => {
  if (typeStore === TypeStore.STORE_CREPICO) {
    return "/store/invoice-receipt/crepico";
  }
  if (typeStore === TypeStore.STORE_PAYGATE) {
    return "/store/invoice-receipt/paygate";
  }
  return "/store/invoice-receipt";
}

export const getStoreSupportUrl = (typeStore: TypeStore, isAdminStore: boolean = true): string => {
  switch (typeStore) {
    case TypeStore.STORE_CREPICO:
      return "https://www.seiko-sol.co.jp/products/crepico/crepico_user/";
    case TypeStore.STORE_PAYGATE:
      return "https://help-paygate.smaregi.jp/hc/ja";
    case TypeStore.STORE_MIGRATE:
      return isAdminStore ? "/admin-store/support" : "/store/support";
    default:
      return "https://www.seiko-sol.co.jp/products/crepico/crepico_user/";
  }
};

export const storeShortcutItems = (typeStore: TypeStore): ShortcutItem[] => [
  {
    title: "振込一覧",
    url: getUrlDeposit(typeStore),
    icon: HamburgerIcon,
    isExternal: false,
    iconClass: "w-6 h-5 md:w-8 md:h-7 mb-2"
  },
  {
    title: "領収書・請求書",
    url: getUrlInvoiceReceipt(typeStore),
    icon: ReceiptIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10 w-6 mb-2"
  },
  {
    title: "ロール紙の購入",
    url: getStoreNotificationRollPaperUrl(typeStore),
    icon: ScrollIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    iconClass: "h-5 md:h-7 mb-2"
  },
  ...(typeStore !== TypeStore.STORE_PAYGATE ? [{
    title: "決済データ",
    url: "/store/crepico-payment",
    icon: ScreenIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10"
  }] : [{
    title: "決済端末管理画面",
    url: "https://member.paygate.ne.jp/login",
    icon: ScreenIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10"
  }]),
  {
    title: "決済端末ヘルプサイト",
    url: getStoreSupportUrl(typeStore),
    icon: BookIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    isModal: typeStore === TypeStore.STORE_CREPICO,
    iconClass: "h-8 md:h-10 mb-2"
  },
  {
    title: "よくあるご質問",
    url: getStoreNotificationFaqUrl(typeStore),
    icon: QuestionIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    iconClass: "h-4 md:h-6 mb-3"
  },
  {
    title: "加盟店情報",
    url: "/store/config",
    icon: SettingsIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10 w-8 mb-2"
  },
  {
    title: "問い合わせ",
    url: "/contact",
    icon: MailIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10 mb-2"
  }
];

export const adminStoreShortcutItems = (typeStore: TypeStore): ShortcutItem[] => [
  {
    title: "振込一覧",
    url: "/admin-store/deposit",
    icon: HamburgerIcon,
    isExternal: false,
    iconClass: "w-6 h-5 md:w-8 md:h-7 mb-2"
  },
  {
    title: "領収書・請求書",
    url: "/admin-store/invoice-receipt",
    icon: ReceiptIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10 w-6 mb-2"
  },
  {
    title: "ロール紙の購入",
    url: getStoreNotificationRollPaperUrl(typeStore),
    icon: ScrollIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    iconClass: "h-5 md:h-7 mb-2"
  },
  ...(typeStore === TypeStore.STORE_PAYGATE ? [{
    title: "端末管理画面",
    url: "https://member.paygate.ne.jp/login",
    icon: ScreenIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10"
  }] : [{
    title: "決済データ",
    url: "/admin-store/crepico-payment",
    icon: ScreenIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10"
  }]),
  {
    title: "決済端末ヘルプサイト",
    url: getStoreSupportUrl(typeStore, true),
    icon: BookIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    isModal: typeStore === TypeStore.STORE_CREPICO,
    iconClass: "h-8 md:h-10 mb-2"
  },
  {
    title: "よくあるご質問",
    url: getStoreNotificationFaqUrl(typeStore),
    icon: QuestionIcon,
    isExternal: typeStore !== TypeStore.STORE_MIGRATE,
    iconClass: "h-4 md:h-6 mb-3"
  },
  // {
  //   title: "エリア・店舗登録",
  //   url: "/admin-store/area-setting",
  //   icon: SettingsIcon,
  //   isExternal: false,
  //   iconClass: "h-8 md:h-10 w-8 mb-2"
  // },
  {
    title: "問い合わせ",
    url: "/contact",
    icon: MailIcon,
    isExternal: false,
    iconClass: "h-8 md:h-10 mb-2"
  }
];