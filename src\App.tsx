import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import AppRouter from "@/routers";
import { isNetworkError } from "@/config/axios";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 3 * 60 * 1000,
      gcTime: 6* 60 * 1000,
      retry: (failureCount, error) => {
        // ✅ Retry nếu là lỗi mạng (không có response)
        // số lần retry < 3
        return failureCount < 3 && isNetworkError(error);
      },
      retryDelay: attemptIndex => Math.min(1000 * 2 ** attemptIndex, 10000), // delay gấp đôi mỗi lần retry, tối đa 10s
      refetchOnWindowFocus: false,
      refetchOnMount: false,
      refetchOnReconnect: true,
    },
  },
});

const App = () => (
  <QueryClientProvider client={queryClient}>
    <TooltipProvider>
      <Toaster />
      <Sonner />
      <AppRouter />
    </TooltipProvider>
  </QueryClientProvider>
);

export default App;
