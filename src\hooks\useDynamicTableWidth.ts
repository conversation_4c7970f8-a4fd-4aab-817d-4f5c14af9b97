import { useState, useEffect } from 'react';

export const useDynamicTableWidth = () => {
  const [tableMaxWidth, setTableMaxWidth] = useState('calc(100vw - 42px)');

  useEffect(() => {
    const calculateMaxWidth = () => {
      const sidebarElement = document.querySelector('[data-state]');
      const isCollapsed = sidebarElement?.getAttribute('data-state') === 'collapsed';
      const isMobile = window.innerWidth < 768;
      
      if (isMobile) {
        setTableMaxWidth('calc(100vw - 42px)');
      } else if (isCollapsed) {
        setTableMaxWidth('calc(100vw - 3rem)'); // collapsed sidebar + padding
      } else {
        setTableMaxWidth('calc(100vw - 16rem - 3rem)'); // expanded sidebar + padding
      }
    };

    calculateMaxWidth();

    // Listen for resize
    window.addEventListener('resize', calculateMaxWidth);

    // Listen for sidebar changes
    const observer = new MutationObserver(calculateMaxWidth);
    const sidebarElement = document.querySelector('[data-state]');
    if (sidebarElement) {
      observer.observe(sidebarElement, { attributes: true, attributeFilter: ['data-state'] });
    }

    return () => {
      window.removeEventListener('resize', calculateMaxWidth);
      observer.disconnect();
    };
  }, []);

  return tableMaxWidth;
};
