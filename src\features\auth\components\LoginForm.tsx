import React from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { useLogin } from '@/features/auth/hooks/useLogin';
import { Link } from 'react-router-dom';
import { Input } from '@/components/ui/input';
import { LoginFormData, loginSchema } from '@/features/auth/schema';
import { Button } from '@/components/ui/button';

export const LoginForm: React.FC = () => {
  const { isLoading, onSubmit } = useLogin();

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<LoginFormData>({
    resolver: zodResolver(loginSchema),
  });

  return (
    <section className="bg-[rgba(246,246,246,1)] shadow-[0px_3px_3px_rgba(0,0,0,0.161)] border self-center flex w-[747px] max-w-full flex-col items-stretch text-[28px] text-[rgba(112,112,112,1)] font-normal whitespace-nowrap mt-[33px] pt-[58px] pb-[92px] px-12 rounded-[17px] border-[rgba(112,112,112,1)] border-solid max-md:px-5">
      <form onSubmit={handleSubmit(onSubmit)} className="flex flex-col">
        <Input
          {...register('email')}
          type="text"
          placeholder="メールアドレス"
          error={errors.email?.message}
          aria-label="メールアドレス"
          aria-invalid={errors.email ? 'true' : 'false'}
        />

        <Input
          {...register('password')}
          type="password"
          placeholder="パスワード"
          error={errors.password?.message}
          aria-label="パスワード"
          aria-invalid={errors.password ? 'true' : 'false'}
          className="mt-11 max-md:mt-10"
        />

        <Button
          type="submit"
          disabled={isLoading || isSubmitting}
          className="flex flex-col text-[28px] bg-[#1D9987] hover:bg-[#1D9987]/90 h-[66px] w-full text-[rgba(246,246,246,1)] mt-[39px] rounded-[13px] transition-opacity focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed max-md:px-5"
          aria-label="ログイン"
        >
          {isLoading ? 'ログイン中...' : 'ログイン'}
        </Button>
      </form>

      <div className="flex flex-col items-center justify-center my-6 text-[rgba(29,153,135,1)]">
        <a href='#'>パスワードを忘れた場合</a>
      </div>

      <hr className='mt-3 border-1 border-gray-600 w-[90%] self-center' />

      <p className="text-[rgba(29,153,135,1)] self-center mt-9 font-bold">
        はじめての方はこちら
      </p>
      
      <Link
          to="/mail/register"
          className="bg-white text-[rgba(29,153,135,1)] border border-1 border-[rgba(29,153,135,1)] h-[66px] px-8 rounded-[13px] mt-[13px] w-full hover:bg-[rgba(29,153,135,1)] hover:text-white hover:font-bold focus:outline-none focus:ring-2 focus:ring-[rgba(25,164,146,1)] focus:ring-offset-2 transition-all duration-200 max-md:px-5 flex items-center justify-center"
          aria-label="新規登録"
        >
        チョキペイIDの新規登録
      </Link>
    </section>
  );
};