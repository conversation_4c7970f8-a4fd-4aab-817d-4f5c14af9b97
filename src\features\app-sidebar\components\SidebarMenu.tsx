import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';
import {
  SidebarContent,
  SidebarGroup,
  SidebarGroupContent,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarMenuSub,
  SidebarMenuSubItem,
  SidebarMenuSubButton,
} from '@/components/ui/sidebar';
import { ChevronRight } from 'lucide-react';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { MenuItem, SIDEBAR_ICONS, SIDEBAR_STYLES } from '@/features/app-sidebar/constants/sidebarConstants';
import { isMenuActive, hasActiveSubItem, isExternalUrl } from '@/features/app-sidebar/utils/sidebarUtils';
import { CrepicoModal } from '@/features/app-sidebar/components/CrepicoModal';

interface SidebarMenuProps {
  menuItems: MenuItem[];
  openSubmenus: Record<string, boolean>;
  onSubmenuToggle: (menuTitle: string) => void;
  onLogout: () => void;
}

export const AppSidebarMenu: React.FC<SidebarMenuProps> = React.memo(({
  menuItems,
  openSubmenus,
  onSubmenuToggle,
  onLogout
}) => {
  const location = useLocation();
  const [crepicoModal, setCrepicoModal] = useState<{ isOpen: boolean; targetUrl: string }>({
    isOpen: false,
    targetUrl: ''
  });

  const handleSubmenuToggle = (menuTitle: string, hasActiveSubItem: boolean) => {
    if (!hasActiveSubItem) {
      onSubmenuToggle(menuTitle);
    }
  };

  const handleCrepicoClick = (e: React.MouseEvent, url: string) => {
    e.preventDefault();
    setCrepicoModal({ isOpen: true, targetUrl: url });
  };

  const handleCrepicoModalClose = () => {
    setCrepicoModal({ isOpen: false, targetUrl: '' });
  };

  return (
    <>
    <SidebarContent>
      <SidebarGroup>
        <SidebarGroupContent>
          <SidebarMenu className='pl-2'>
            {menuItems.map((item) => {
              const isActive = isMenuActive(location.pathname, item.url);
              const hasActiveSub = hasActiveSubItem(location.pathname, item.subItems);

              if (item.subItems) {
                return (
                  <Collapsible 
                    key={item.title} 
                    asChild 
                    open={hasActiveSub || openSubmenus[item.title]}
                    onOpenChange={() => handleSubmenuToggle(item.title, hasActiveSub)}
                    defaultOpen={false}
                  >
                    <SidebarMenuItem>
                      <SidebarMenuButton
                        asChild
                        className={`w-full ${isActive || hasActiveSub ? SIDEBAR_STYLES.menuActive : SIDEBAR_STYLES.menuInactive}`}
                      >
                        <CollapsibleTrigger className="flex items-center gap-3 px-3 py-5 my-1 w-full">
                          <img src={item.icon} alt={item.title} className="w-6 h-6" />
                          <span className="text-[20px] flex-1">{item.title}</span>
                          <ChevronRight className="ml-auto transition-transform duration-200 group-data-[state=open]/collapsible:rotate-90" />
                        </CollapsibleTrigger>
                      </SidebarMenuButton>
                      <CollapsibleContent>
                        <SidebarMenuSub>
                          {item.subItems.map((subItem) => {
                            const isSubActive = isMenuActive(location.pathname, subItem.url);
                            const isExternal = isExternalUrl(subItem.url);

                            return (
                              <SidebarMenuSubItem key={subItem.title}>
                                <SidebarMenuSubButton
                                  asChild
                                  className={isSubActive ? SIDEBAR_STYLES.subMenuActive : SIDEBAR_STYLES.subMenuInactive}
                                >
                                  {subItem.isShowModal ? (
                                    <button
                                      onClick={(e) => handleCrepicoClick(e, subItem.url)}
                                      className="flex items-center gap-3 pl-1 pr-0 py-1 w-full text-left"
                                    >
                                      <span className="text-[18px]">{subItem.title}</span>
                                    </button>
                                  ) : isExternal ? (
                                    <a
                                      href={subItem.url}
                                      target="_blank"
                                      rel="noopener noreferrer"
                                      className="flex items-center gap-3 pl-1 pr-0 py-1"
                                    >
                                      <span className="text-[18px]">{subItem.title}</span>
                                    </a>
                                  ) : (
                                    <Link to={subItem.url} target={subItem.url === '/contact' && 'blank'} className="flex items-center gap-3 pl-1 pr-0 py-1">
                                      <span className="text-[18px]">{subItem.title}</span>
                                    </Link>
                                  )}
                                </SidebarMenuSubButton>
                              </SidebarMenuSubItem>
                            );
                          })}
                        </SidebarMenuSub>
                      </CollapsibleContent>
                    </SidebarMenuItem>
                  </Collapsible>
                );
              } else {
                const isExternal = isExternalUrl(item.url);

                return (
                  <SidebarMenuItem key={item.title}>
                    <SidebarMenuButton
                      asChild
                      className={`w-full ${isActive ? SIDEBAR_STYLES.menuActive : SIDEBAR_STYLES.menuInactive}`}
                    >
                      {isExternal ? (
                        <a
                          href={item.url}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center gap-3 px-3 py-5 my-1"
                        >
                          <img src={item.icon} alt={item.title} className="w-6 h-6" />
                          <span className="text-[20px]">{item.title}</span>
                        </a>
                      ) : (
                        <Link to={item.url} className="flex items-center gap-3 px-3 py-5 my-1">
                          <img src={item.icon} alt={item.title} className="w-6 h-6" />
                          <span className="text-[20px]">{item.title}</span>
                        </Link>
                      )}
                    </SidebarMenuButton>
                  </SidebarMenuItem>
                );
              }
            })}

            {/* Logout menu item */}
            <SidebarMenuItem>
              <SidebarMenuButton
                asChild
                className={`w-full ${SIDEBAR_STYLES.menuInactive}`}
              >
                <button onClick={onLogout} className="flex items-center gap-3 px-3 py-5 my-1">
                  <img src={SIDEBAR_ICONS.logout} alt="ログアウト" className="w-6 h-6" />
                  <span className="text-[20px]">ログアウト</span>
                </button>
              </SidebarMenuButton>
            </SidebarMenuItem>
          </SidebarMenu>
        </SidebarGroupContent>
      </SidebarGroup>
    </SidebarContent>

    {/* CREPICO Modal */}
    <CrepicoModal
      isOpen={crepicoModal.isOpen}
      onClose={handleCrepicoModalClose}
      targetUrl={crepicoModal.targetUrl}
    />
  </>
  );
});

AppSidebarMenu.displayName = 'AppSidebarMenu'; 