import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON> } from "@/components/ui/tabs";
import BankInformation from "@/features/config/components/BankInformation";
import CorporateInformation from "@/features/config/components/CorporateInformation";
import RepresentativeInformation from "@/features/config/components/RepresentativeInformation";
import StoreInformation from "@/features/config/components/StoreInformation";
import { useQueryMerchantStatus } from "@/features/config/hooks/useQueryMerchantStatus";
import { useAuthStore } from "@/store";
import AdditionalInformation from "@/features/config/components/AdditionalInformation";
import ChokipayInformation from "@/features/config/components/ChokipayInformation";
import BillingInformation from "@/features/status/components/BillingInformation";
import ChokipayInformationNew from "@/features/config/components/ChokipayInformationNew";
import ChokipayInformationMigrate from "@/features/config/components/ChokipayInformationMigrate";
import { AccountTypes } from "@/types/globalType";
import { formatDateJapan } from "@/utils/dateUtils";
import { LoadingSpinner } from "@/components/LoadingSpinner";
import { Link } from "react-router-dom";

const Config = () => {
    const { user } = useAuthStore();

    // get merchant status data
    const { data: merchantStatusData, isLoading } = useQueryMerchantStatus({
        agxMerchantNo: user?.agxMerchantNo || "",
    });

    // get merchant crepico data
    const { data: merchantCrepicoData, isLoading: isLoadingCrepico } = useQueryMerchantStatus({
        agxMerchantNo: user?.agxNewMerchantNo || "",
    });

    const tabOptions = [
        { value: "choqipay", label: "チョキペイ 情報" },
        { value: "billing", label: "業種・事業形態情報" },
        { value: "legal", label: "法人情報" },
        { value: "representative", label: "代表者情報" },
        { value: "store", label: "店舗情報" },
        { value: "bank", label: "銀行情報" },
        { value: "security", label: "セキュリティ情報" }
    ];

    const generateStatus = () => {
        if (user.statusAccount === AccountTypes.APPLICATION_COMPLETE) {
            return `ステータス ｜ お申込み受付 ${user.merchantRegistrationDate ? `｜ ${formatDateJapan(user.merchantRegistrationDate)}` : ''}`;
        }
        return "ステータス ｜ 審査完了";
    }

    if (isLoading || isLoadingCrepico) {
        return <LoadingSpinner />;
    }

    return (
        <div className="w-full px-4 py-4">
            <div className="mx-auto space-y-6 md:space-y-8 lg:space-y-10">
                <div>
                    <div className="lg:flex items-center justify-between w-full mt-2 mb-4 ">
                        <span className="text-[18px] md:text-[24px] text-[#6F6F6E] font-normal">
                            加盟店情報の確認と変更ができます
                        </span>
                        <div className="lg:flex gap-x-8 pt-3 lg:pt-0 xl:pr-20">
                            <Link to="/store/change-store-information" className="text-[18px] md:text-[20px] lg:text-[24px] text-[#1D9987] underline underline-offset-4 decoration-[#1D9987]/80 hover:text-[#1D9987]/90 transition-colors">
                             加盟店情報変更フォーム
                            </Link>
                            <h2 
                                className="text-[18px] md:text-[20px] lg:text-[24px] text-[#1D9987] underline underline-offset-4 decoration-[#1D9987]/80 hover:text-[#1D9987]/90 transition-colors cursor-pointer pt-4 lg:pt-0"
                                onClick={() => window.open("/contact")} 
                            >
                                問い合わせフォーム
                            </h2>
                        </div>
                    </div>
                    <hr className="border-gray-500 mb-6 border-1 w-full" />
                    {/* Header */}
                    <div className="flex justify-between items-center pb-12 w-full">
                        <div className="text-[24px] font-[400]">{generateStatus()}</div>
                    </div>
                    {/* Tabs Navigation */}
                    <Tabs defaultValue="choqipay" className="w-full lg:w-full xl:w-[95%] text-[20px]">
                        <TabsList className="flex w-full justify-between bg-white rounded-none h-12 border-b-2 pb-[0px] overflow-auto">
                            {tabOptions.map((option) => (
                            <TabsTrigger
                                value={option.value}
                                className="group text-[24px] max-h-full items-end text-[#6F6F6E] data-[state=active]:text-teal-700 data-[state=active]:border-b-2 data-[state=active]:border-teal-500 border-t-0 border-l-0 border-r-0 flex rounded-none pt-1 pl-1 pr-1"
                                key={option.value}
                                style={{ display: merchantStatusData?.agxBusinessForm === 283260001 && option.value == "legal" ? 'none' : '' }}
                            >
                                <div className="text-[24px] rounded-tl-md rounded-tr-md px-2 md:px-3 lg:px-4 xl:px-0">
                                    {option.label}
                                </div>
                            </TabsTrigger>
                            ))}
                        </TabsList>
                        <TabsContent value="legal">
                            <CorporateInformation merchantData={merchantStatusData} />
                        </TabsContent>
                        <TabsContent value="representative">
                            <RepresentativeInformation merchantData={merchantStatusData} />
                        </TabsContent>
                        <TabsContent value="store">
                            <StoreInformation merchantData={merchantStatusData} />
                        </TabsContent>
                        <TabsContent value="bank">
                            <BankInformation merchantData={merchantStatusData} />
                        </TabsContent>
                        <TabsContent value="security">
                            <AdditionalInformation merchantData={merchantStatusData} />
                        </TabsContent>
                        <TabsContent value="choqipay">
                            {user.memberType
                                ?
                                <ChokipayInformationNew merchantData={merchantStatusData} />
                                :
                                (user.agxNewMerchantNo ? 
                                    <ChokipayInformationMigrate merchantData={merchantStatusData} merchantCrepicoData={merchantCrepicoData}/>
                                :
                                    <ChokipayInformation merchantData={merchantStatusData}  merchantCrepicoData={merchantCrepicoData}/>)
                            }
                        </TabsContent>
                        <TabsContent value="billing">
                            <BillingInformation merchantData={merchantStatusData} />
                        </TabsContent>
                    </Tabs>
                </div>
            </div>
        </div>
    );
}

export default Config;