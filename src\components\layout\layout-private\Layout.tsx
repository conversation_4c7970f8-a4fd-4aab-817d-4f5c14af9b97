import React from 'react';
import { AppSidebar } from '@/components/layout/layout-private/Sidebar';
import { Header } from '@/components/layout/layout-private/Header';
import { SidebarProvider, SidebarInset } from '@/components/ui/sidebar';
import { Footer } from '@/components/layout/Footer';
import { BreadcrumbFooter } from './BreadcrumbFooter';

interface LayoutProps {
  children: React.ReactNode;
}

export const Layout: React.FC<LayoutProps> = ({ children }) => {
  return (
    <div className='flex flex-col h-screen'>
      <div>
        <SidebarProvider>
          <div className="flex w-full">
            <AppSidebar />
            <SidebarInset className="flex flex-col flex-1 mb-[100px] md:mb-[83px] min-h-[calc(100vh-100px)] md:min-h-[calc(100vh-90px)] w-full">
              <Header />
              <main className="flex-1 lg:px-4 md:px-4 px-1 md:pb-4 pb-4 overflow-y-auto">
                {children}
              </main>
              <BreadcrumbFooter />
            </SidebarInset>
          </div>
        </SidebarProvider>
      </div>
      <Footer />
    </div>
  );
};