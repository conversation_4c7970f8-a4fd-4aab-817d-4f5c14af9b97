import React, { useState } from "react";
import logoIndex03 from "/images/logo_index03.png";
import { useNavigate } from "react-router-dom";

function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const navigate = useNavigate();

  const handleRedirect = () => {
    navigate("/signin", { replace: true });
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  return (
    <div className="fixed top-0 w-full bg-white z-[9999] shadow-[0_0_7px_rgba(0,0,0,0.1)] transition-all duration-400">
      <div className="container px-4 sm:px-2 max-w-[1140px]">
        <section className="flex items-center justify-between py-7 sm:py-4 md:h-[100px] h-[45px]">
          <div className="lg:w-[152px] w-[120px] relative z-10 transition-all duration-400">
            <a href="https://choqi.co.jp/choqipay/">
              <img
                src={logoIndex03}
                alt="小さな感動をもっと。チョキペイ"
                className="w-full align-middle"
              />
            </a>
          </div>
          <div className="hidden sm:block sm:w-[76px]"></div>
          <nav className="hidden md:flex layout-contact_nav">
            <ul className="flex items-center space-x-5 lg:space-x-5">
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base hover:no-underline lg:text-sm py-2 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/#reason"
                >
                  選ばれる理由
                </a>
              </li>
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base lg:text-sm py-2 hover:no-underline relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/#features"
                >
                  チョキペイの特長
                </a>
              </li>
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base lg:text-sm py-2 hover:no-underline relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/#pay"
                >
                  決済方法
                </a>
              </li>
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base lg:text-sm py-2 hover:no-underline relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/#flow"
                >
                  導入の流れ
                </a>
              </li>
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base lg:text-sm py-2 hover:text-blue-800 hover:opacity-100 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/#flow"
                >
                  導入事例
                </a>
              </li>
              <li>
                <a
                  className="text-[#505050] font-['Montserrat'] font-bold text-base lg:text-sm py-2 hover:text-blue-800 hover:opacity-100 relative after:content-[''] after:absolute after:bottom-0 after:left-0 after:w-0 after:h-[2px] after:bg-[#13ae9c] after:transition-all after:duration-250 hover:after:w-full"
                  href="https://choqi.co.jp/choqipay/faq/faq_list1.html"
                >
                  よくあるご質問
                </a>
              </li>
              <li className="mt-[-4px]">
                <button
                  id="loginBtn"
                  type="button"
                  onClick={handleRedirect}
                  className="text-[#13ae9c] h-[30px] border-2 border-[#13ae9c] rounded-[15px] w-full bg-white hover:bg-[#13ae9c] hover:text-white transition-all duration-300 flex items-center justify-center"
                >
                  <span className="bg-[url('/images/ico_login01.svg')] hover:bg-[url('/images/ico_login02.svg')] bg-no-repeat bg-[center_left_15%] bg-[length:16px_16px] pl-10 pr-6">
                    LOGIN
                  </span>
                </button>
              </li>
            </ul>
          </nav>
        </section>
        <div className="md:hidden">
          <p
            className={`fixed top-5 right-4 w-[22px] h-[18px] z-[9999] transition-all duration-400 ${
              isMenuOpen ? "active" : ""
            }`}
            onClick={toggleMenu}
          >
            <span className="absolute w-full h-[2px] bg-[#14af9c] top-0 left-0 right-0 transition-all duration-400 group-[.active]:bg-white group-[.active]:translate-y-2 group-[.active]:-rotate-45"></span>
            <span className="absolute w-full h-[2px] bg-[#14af9c] top-2 left-0 right-0 transition-all duration-400 group-[.active]:opacity-0"></span>
            <span className="absolute w-full h-[2px] bg-[#14af9c] bottom-0 left-0 right-0 transition-all duration-400 group-[.active]:bg-white group-[.active]:-translate-y-2 group-[.active]:rotate-45"></span>
          </p>
        </div>
        <nav
          id="menu"
          className={`${
            isMenuOpen ? "block" : "hidden"
          } fixed top-0 left-0 right-0 mt-[45px] pb-[45px] bg-[rgba(19,174,156,0.85)] z-[9998] overflow-y-scroll`}
        >
          <div className="flex flex-col items-stretch justify-between w-full h-full">
            <ul className="spMenu">
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/#reason"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  選ばれる理由
                </a>
              </li>
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/#features"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  チョキペイの特長
                </a>
              </li>
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/#pay"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  決済方法
                </a>
              </li>
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/#flow"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  導入の流れ
                </a>
              </li>
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/#flow"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  導入事例
                </a>
              </li>
              <li>
                <a
                  href="https://choqi.co.jp/choqipay/faq/faq_list1.html"
                  className="block text-white font-['Montserrat'] font-bold text-xl p-5 text-center"
                >
                  よくあるご質問
                </a>
              </li>
            </ul>
            <div className="bg-[rgba(255,255,255,0.95)] p-5">
              <p className="text-[#13ae9c] text-base font-bold pb-1.5 border-b-2 border-[#13ae9c] flex items-center">
                <img src="/images/ico_login01.svg" alt="" className="mr-1.5" />
                LOGIN
              </p>
              <ul className="pt-6 pb-2.5">
                <li className="mb-5">
                  <a
                    href="https://choqipay.powerappsportals.com/SignIn"
                    className="block text-[#13ae9c] font-['YuGothic','YuGothicM','Hiragino_Kaku_Gothic_ProN','Meiryo',sans-serif] font-bold text-center"
                  >
                    チョキペイ
                  </a>
                </li>
                <li>
                  <a
                    href="https://choqipay-online.powerappsportals.com/SignIn"
                    className="block text-[#13ae9c] font-['YuGothic','YuGothicM','Hiragino_Kaku_Gothic_ProN','Meiryo',sans-serif] font-bold text-center"
                  >
                    チョキペイOnline
                  </a>
                </li>
              </ul>
            </div>
          </div>
        </nav>
      </div>
    </div>
  );
}

export default Header;
