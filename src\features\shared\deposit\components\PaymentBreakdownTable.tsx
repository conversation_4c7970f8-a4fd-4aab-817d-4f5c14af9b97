import React from 'react';
import { Link } from 'react-router-dom';
import { Table, TableBody, TableCell, TableRow, TableHead, TableHeader } from '@/components/ui/table';
import { formatNumber } from '@/utils/dateUtils';
import { mapTransactionType } from '@/constants/common.constant';
import {
  isCredictCardTransaction,
  isElectronicMoneyTransaction,
  isQRCodeTransaction
} from '@/features/shared/deposit/utils/transactionUtils';
import { useDynamicTableWidth } from '@/hooks/useDynamicTableWidth';
import { STORE } from '@/types/globalType';

interface PaymentBreakdownItem {
  agxTransactionType: number;
  agxPaymentBreakdownId: string;
  agxNumberOfSales: number;
  agxSalesAmount: number;
  agxTotalFeeRate: number;
  agxTotalFee: number;
  tax?: number; // For store view
  agxInHouseTax?: number; // For admin view
  agxPaymentAmount: number;
  groupCodeName?: string;
  agxMerchantNo?: string; // For admin view
}

interface PaymentBreakdownTableProps {
  paymentBreakdowns: PaymentBreakdownItem[];
  transferDate: string;
  type?: string;
  agxMerchantNo?: string;
  title?: string;
  isAdminView?: boolean; // To differentiate between admin and store views
}

export const PaymentBreakdownTable: React.FC<PaymentBreakdownTableProps> = ({
  paymentBreakdowns,
  transferDate,
  type,
  agxMerchantNo,
  title = "決済種別ごとのデータ",
  isAdminView = false
}) => {
  const tableMaxWidth = useDynamicTableWidth();
  if (!paymentBreakdowns) return null;

  // Group payment breakdowns by category
  const creditCardItems = paymentBreakdowns.filter(item =>
    isCredictCardTransaction(item.agxTransactionType)
  );
  const qrCodeItems = paymentBreakdowns.filter(item =>
    isQRCodeTransaction(item.agxTransactionType)
  );
  const electronicMoneyItems = paymentBreakdowns.filter(item =>
    isElectronicMoneyTransaction(item.agxTransactionType)
  );

  const renderCategoryGroup = (categoryName: string, items: PaymentBreakdownItem[]) => {
    if (items.length === 0) return null;

    return (
      <>
        {/* Category header row */}
        <TableRow key={`${categoryName}-header`} className='border-none hover:bg-white'>
          <TableCell className="py-3 px-2 text-left text-black font-medium">
            {categoryName}
          </TableCell>
        </TableRow>
        {/* Category items */}
        {items.map((item, index) => (
          <TableRow
            key={`${categoryName}-${index}`}
            className={`border-none ${index % 2 !== 0 ? 'bg-gray-100 hover:bg-gray-100' : 'hover:bg-white'}`}
          >
            <TableCell className="py-3 px-2 text-left pl-6">
              <div className="flex items-center gap-2">
                <Link
                  to={isAdminView
                    ? `/admin-store/deposit/detail/${transferDate}/${item.agxTransactionType}/${item.agxMerchantNo}/${btoa(item.agxPaymentBreakdownId)}`
                    : `/store/deposit/${type}/detail/${btoa(agxMerchantNo || '')}/${btoa(item.agxPaymentBreakdownId)}/${item.agxTransactionType}/${transferDate}`
                  }
                  className="text-[#6F6F6E] hover:text-[#6F6F6E]/80"
                >
                  {isAdminView
                    ? `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName || ''}`
                    : `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName ? ` (${item.groupCodeName})` : ''}`
                  }
                </Link>
                {/* Display payment method images */}
                {/* <div className="flex gap-1 ml-2">
                  {mapTransactionImage.get(item.agxTransactionType)?.map((iconSrc, iconIndex) => (
                    <img
                      key={iconIndex}
                      src={iconSrc}
                      alt={`Payment method ${iconIndex + 1}`}
                      className="ml-1"
                    />
                  ))}
                </div> */}
              </div>
            </TableCell>
            {isAdminView && (
              <TableCell className="py-3 px-2 text-center">
                {item.agxMerchantNo}
              </TableCell>
            )}
            <TableCell className="py-3 px-2 text-center">
              {item.agxNumberOfSales !== null && item.agxNumberOfSales !== undefined ? `${formatNumber(item.agxNumberOfSales)}件` : ''}
            </TableCell>
            <TableCell className="py-3 px-2 text-center">
              {item.agxSalesAmount !== null && item.agxSalesAmount !== undefined ? `${formatNumber(item.agxSalesAmount)}円` : ''}
            </TableCell>
            <TableCell className="py-3 px-2 text-center">
              {item.agxTotalFeeRate !== null && item.agxTotalFeeRate !== undefined ? `${Number(item.agxTotalFeeRate).toFixed(2)}%` : ''}
            </TableCell>
            <TableCell className="py-3 px-2 text-center">
              {item.agxTotalFee !== null && item.agxTotalFee !== undefined ? `${formatNumber(item.agxTotalFee)}円` : ''}
            </TableCell>
            <TableCell className="py-3 px-2 text-center">
              {isAdminView
                ? (item.agxInHouseTax !== null && item.agxInHouseTax !== undefined ? `(${formatNumber(item.agxInHouseTax)})円` : '')
                : (item.tax !== null && item.tax !== undefined ? `(${formatNumber(item.tax)})円` : '')
              }
            </TableCell>
            <TableCell className="py-3 px-2 text-center">
              {item.agxPaymentAmount !== null && item.agxPaymentAmount !== undefined ? `${formatNumber(item.agxPaymentAmount)}円` : ''}
            </TableCell>
          </TableRow>
        ))}
        {renderSpacerRow()}
      </>
    );
  };

  const renderSpacerRow = () => (
    <TableRow className="border-b border-[#6F6F6E] hover:bg-white">
      <TableCell className="py-2 px-2"></TableCell>
    </TableRow>
  );

  return (
    <div className="w-full">
      <div className="text-2xl text-[#6F6F6E] md:p-2 lg:px-4">{title}</div>
      <div className="bg-white w-full overflow-x-auto" style={{ maxWidth: tableMaxWidth }}>
        <Table className="text-xl text-[#6F6F6E] min-w-[1200px] max-w-[1520px] table-fixed">
          <TableHeader>
            <TableRow className="border-none">
              <TableHead className="text-center bg-white font-medium w-[28%] pl-0">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  {isAdminView ? '取引区分' : '決済種別'}
                </span>
              </TableHead>
              {isAdminView && (
                <TableHead className="px-1 text-center bg-white font-medium w-[10%]">
                  <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                    加盟店番号
                  </span>
                </TableHead>
              )}
              <TableHead className="text-center bg-white font-medium w-[10%]">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  売上件数
                </span>
              </TableHead>
              <TableHead className="text-center bg-white font-medium w-[10%]">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  売上金額
                </span>
              </TableHead>
              <TableHead className="text-center bg-white font-medium w-[10%]">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  手数料率
                </span>
              </TableHead>
              <TableHead className="text-center bg-white font-medium w-[10%]">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  手数料額
                </span>
              </TableHead>
              <TableHead className="text-center bg-white font-medium w-[12%] px-0">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  {isAdminView ? '（内消費税額）' : '（内消費税）'}
                </span>
              </TableHead>
              <TableHead className="pr-0 text-center bg-white font-medium w-[10%]">
                <span className="w-full inline-block border-b border-[#6F6F6E] py-3 text-[#6F6F6E]">
                  {isAdminView ? '振込額' : (type === STORE.PAYGATE ? '振込金額' : '振込額')}
                </span>
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {renderCategoryGroup('クレジットカード', creditCardItems)}
            {renderCategoryGroup('QRコード決済', qrCodeItems)}
            {renderCategoryGroup('電子マネー', electronicMoneyItems)}
            {renderSpacerRow()}
          </TableBody>
        </Table>
      </div>
    </div>
  );
};
