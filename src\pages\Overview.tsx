import flagIcon from "@/assets/images/グループ 1350.svg";
import { Link } from "react-router-dom";
import { useGetUrlRedirect } from "@/features/overview";
import { TypeStore } from "@/types/globalType";
import { Notification } from "@/components/Notification";
import { CrepicoModal } from "@/features/app-sidebar";
import { useState } from "react";

const ShortcutsMenu = () => {
  const { shortcutItems } = useGetUrlRedirect();
  const [crepicoModal, setCrepicoModal] = useState<{ isOpen: boolean; targetUrl: string }>({
    isOpen: false,
    targetUrl: ''
  });

  const handleCrepicoClick = (e: React.MouseEvent, url: string) => {
    e.preventDefault();
    setCrepicoModal({ isOpen: true, targetUrl: url });
  };

  const handleCrepicoModalClose = () => {
    setCrepicoModal({ isOpen: false, targetUrl: '' });
  };

  return (
    <>
      <div className="flex items-center py-6">
        <img src={flagIcon} alt="" />
        <h2 className="text-xl md:text-2xl text-[#6F6F6E]">
          ショートカット
        </h2>
      </div>

      {/* Shortcuts Grid */}
      <div className="w-full md:w-[100%] lg:w-[90%] xl:w-[70%] grid grid-cols-1 sm:grid-cols-2 md:grid-cols-2 xl:grid-cols-4 gap-6 xl:gap-7">
        { shortcutItems.map((item, index) => {
          if (item.isExternal) {
            if(item.isModal) {
              return (
                <button
                    key={index}
                    onClick={(e) => handleCrepicoClick(e, item.url)}
                    className="shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
                  >
                    <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                      <img src={item.icon} alt="" className={item.iconClass} />
                      <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">{item.title}</span>
                    </div>
                </button>
              )
            }
            return (
              <a key={index} href={item.url} target="blank" className="shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white">
                <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                  <img src={item.icon} alt="" className={item.iconClass} />
                  <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">{item.title}</span>
                </div>
              </a>
            )
          }
          return (
            <Link 
              key={index}
              to={item.url}
              target={item.url === '/contact' ? 'blank' : ''}
              className="shadow-md transition-shadow cursor-pointer border border-gray-500 rounded-lg bg-white"
            >
              <div className="p-3 md:p-4 flex flex-col justify-center items-center h-20 md:h-24">
                <img src={item.icon} alt="" className={item.iconClass} />
                <span className="text-[#6F6F6E] text-[18px] md:text-[20px] lg:text-[22px] text-center">{item.title}</span>
              </div>
            </Link>
          )
          })
        }
      </div>

      {/* CREPICO Modal */}
      <CrepicoModal
        isOpen={crepicoModal.isOpen}
        onClose={handleCrepicoModalClose}
        targetUrl={crepicoModal.targetUrl}
      />
    </>
  )
}

const Overview = () => {
  const { merchantNo, storeName } = useGetUrlRedirect();

  return (
    <div className="min-h-[calc(100vh-180px)] p-4 md:py-6 md:px-1 xl:p-6 text-[20px]">
      <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">
        こんにちは、加盟店番号{merchantNo} {storeName}さん
      </h2>
      <div className="py-6 mt-2">
        <Notification />
        <ShortcutsMenu />
      </div>
    </div>
  );
}

export default Overview;
