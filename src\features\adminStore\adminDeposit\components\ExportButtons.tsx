import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Download } from 'lucide-react';
import { CSVLink } from 'react-csv';
import { AdminDepositData, CSVExportData } from '../types';
import { mapTransactionType } from '@/constants/common.constant';

interface ExportButtonsProps {
  data: AdminDepositData;
  transferDate: string;
  dlEnable: boolean;
  onExportPDF: () => void;
}

export const ExportButtons: React.FC<ExportButtonsProps> = ({
  data,
  transferDate,
  dlEnable,
  onExportPDF
}) => {
  // CSV Export headers
  const headers = [
    { label: '取引区分', key: 'agxTransactionType' },
    { label: '加盟店番号', key: 'merchantNumber' },
    { label: '売上件数', key: 'agxNumberOfSales' },
    { label: '売上金額', key: 'agxSalesAmount' },
    { label: '手数料率', key: 'agxTotalFeeRate' },
    { label: '手数料額', key: 'agxTotalFee' },
    { label: '（内消費税額）', key: 'agxInHouseTax' },
    { label: '振込額', key: 'agxPaymentAmount' }
  ];

  const getCSVData = (): CSVExportData[] => {
    return data.agxPaymentBreakdowns.map(item => ({
      agxTransactionType: `${mapTransactionType.get(item.agxTransactionType)}${item.groupCodeName}`,
      merchantNumber: `${item.agxStoreName}-${item.agxMerchantNo}`,
      agxNumberOfSales: item.agxNumberOfSales,
      agxSalesAmount: item.agxSalesAmount,
      agxTotalFeeRate: `${Number(item.agxTotalFeeRate).toFixed(2)}%`,
      agxTotalFee: item.agxTotalFee,
      agxInHouseTax: `(${item.agxInHouseTax})`,
      agxPaymentAmount: item.agxPaymentAmount
    }));
  };

  // CSV export configuration
  const csvExport = {
    data: getCSVData(),
    headers: headers,
    filename: `deposit-${transferDate}.csv`,
    separator: ',', // Sử dụng separator tùy chỉnh
    enclosingCharacter: '' // Bỏ dấu ngoặc kép bao quanh
  };

  const [isHovered, setIsHovered] = useState(false);

  return (
    <div className="relative">
      <div
        className="relative"
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        {/* Download Icon Button */}
        <button
          className="p-2 text-[#1D9987] hover:text-[#1D9987]/80 hover:opacity-80 rounded transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
          disabled={!dlEnable}
        >
          <Download className="h-9 w-9" />
        </button>

        {/* Dropdown Menu */}
        {isHovered && dlEnable && (
          <div className="absolute top-full left-1/2 transform -translate-x-1/2 w-32 bg-white border border-gray-300 rounded shadow-md z-50">
            {/* @ts-expect-error : type mismatch due to version node */}
            <CSVLink
              data={csvExport.data}
              headers={csvExport.headers}
              filename={csvExport.filename}
              separator={csvExport.separator}
              enclosingCharacter={csvExport.enclosingCharacter}
              className="block w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 border-b border-gray-200 transition-colors duration-150 no-underline"
            >
              CSV
            </CSVLink>
            <button
              onClick={onExportPDF}
              className="w-full px-6 py-3 text-center text-sm text-gray-700 hover:bg-blue-50 hover:text-blue-700 transition-colors duration-150"
            >
              PDF
            </button>
          </div>
        )}
      </div>
    </div>
  );
};
