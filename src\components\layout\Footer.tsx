import React from 'react';

export const Footer: React.FC = () => {
  return (
    <footer className="fixed bottom-0 left-0 w-full bg-[#f5f5f5] z-50">
      <div className="w-full py-2 md:px-8 xl:px-32 lg:px-10 lg:py-4">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 md:gap-0">
          <div className="flex items-center gap-2 md:gap-3">
            <img 
              src="/footer_logo.png" 
              alt="ChoQi Footer Logo" 
              className="h-auto object-contain"
            />
            <div className="text-sm md:text-lg lg:text-xl text-gray-600">
              © ChoQi Co., Ltd.
            </div>
          </div>
            <div className="flex flex-row items-center gap-1 sm:gap-2 md:gap-8 text-xs sm:text-sm md:text-lg lg:text-xl text-[#1D9987]">
              <a href="https://choqi.co.jp/privacypolicy.html" className="hover:underline transition-colors text-center">
                プライバシーポリシー
              </a>
            </div>
          {/* { isPrivate ? 
            <div className="flex flex-row items-center gap-1 sm:gap-2 md:gap-8 text-xs sm:text-sm md:text-lg lg:text-xl text-[#1D9987]">
              <a href="https://choqi.co.jp/choqipay/faq/faq_list12.html" className="hover:underline transition-colors text-center">
                よくあるご質問
              </a>
              <span className="text-[14px] sm:text-[16px] md:text-[20px] text-gray-500">｜</span>
              <a href="https://choqi.co.jp/privacypolicy.html" className="hover:underline transition-colors text-center">
                プライバシーポリシー
              </a>
              <span className="text-[14px] sm:text-[16px] md:text-[20px] text-gray-500">｜</span>
              <Link to="/store/agreement" className="hover:underline transition-colors text-center">
                加盟店規約
              </Link>
            </div> : 
            <div className="flex flex-row items-center gap-1 sm:gap-2 md:gap-8 text-xs sm:text-sm md:text-lg lg:text-xl text-[#1D9987]">
              <a href="https://choqi.co.jp/privacypolicy.html" className="hover:underline transition-colors text-center">
                プライバシーポリシー
              </a>
            </div>
          } */}
        </div>
      </div>
    </footer>
  );
}; 