import { useAuthStore } from "@/features/auth/slices/authStore";
import { AccountTypes } from "@/types/globalType";
import { useQuery } from "@tanstack/react-query";
import { notificationService } from "@/features/overview";

interface Notification {
  url: string,
  title: string,
  isActive: boolean
}

export const useGetNotificationInvoiceDeposit = (merchantNo: string) => {
  // Query for get notification app
  const {
    data,
    isLoading,
    error
  } = useQuery({
    queryKey: ['notification-app', merchantNo],
    queryFn: () => notificationService.getNotificationApp(btoa(merchantNo)),
    enabled: !!merchantNo,
  });

  return {
    notificationData: data,
  }
}