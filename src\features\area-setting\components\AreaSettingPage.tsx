import { useAuthStore } from '@/store';
import _ from 'lodash';
import { useEffect, useState } from 'react'
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { useDeleteAgxArea } from '../hooks/useDeleteAgxArea';
import { useDeleteAgxSubArea } from '../hooks/useDeleteAgxSubArea';
import { Button } from '@/components/ui/button';
import { ConfirmDeleteModal } from './ConfirmDeleteModal';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNavigate } from 'react-router-dom';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

const initialState = {
  agxAreas: [],
  agxSubAreas: [],
  agxSubAreaModal: [],
  loading: true,
  error: false
}

export const AreaSettingPage = () => {
  const navigate = useNavigate();
  const { user } = useAuthStore();
  const [dataAreaSetting, setDataAreaSetting] = useState(initialState);

  const [deleteDialog, setDeleteDialog] = useState<{ type: 'area' | 'subarea' | null, id: string | null }>({ type: null, id: null });
  const [isDialogOpen, setIsDialogOpen] = useState(false);

  const { data: GetAreaSettingResponse, isLoading, refetch } = useQueryAgxArea({
    agxMerchantNo: user?.agxMerchantNo || "",
  });

  const { deleteAgxAreaAsync } = useDeleteAgxArea();
  const { deleteAgxSubAreaAsync } = useDeleteAgxSubArea();

  useEffect(() => {
    getDataAreaSetting();
  }, [GetAreaSettingResponse])

  useEffect(() => {
    refetch();
  }, [])

  const getDataAreaSetting = async () => {
    try {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: GetAreaSettingResponse.agxAreas,
        agxSubAreas: GetAreaSettingResponse.agxSubAreas,
        agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
        loading: false,
        error: false
      });
    } catch (error) {
      setDataAreaSetting({
        ...dataAreaSetting,
        agxAreas: [],
        agxSubAreas: [],
        agxSubAreaModal: [],
        loading: true,
        error: true
      })
    }
  }

  const handleDeleteArea = (agxAreaId: string) => {
    setDeleteDialog({ type: 'area', id: agxAreaId });
    setIsDialogOpen(true);
  };

  const handleDeleteSubArea = (agxSubAreaId: string) => {
    setDeleteDialog({ type: 'subarea', id: agxSubAreaId });
    setIsDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    if (deleteDialog.type === 'area' && deleteDialog.id) {
      await deleteAgxAreaAsync(deleteDialog.id);
      await refetch();
    } else if (deleteDialog.type === 'subarea' && deleteDialog.id) {
      await deleteAgxSubAreaAsync(deleteDialog.id);
      await refetch();
    }
    setIsDialogOpen(false);
    setDeleteDialog({ type: null, id: null });
  };

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <div className="pt-6 pb-2 px-2">
      <div className="max-w-full mx-auto px-4 sm:px-6 md:px-8 lg:px-[5%] xl:[mx-20] lg:mt-16 sm:mt-10 mt-4 xl:!mr-[13%]">
        
        {/* Area Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 border-b border-[#707070] pb-4 space-y-3 sm:space-y-0">
          <h3 className="font-semibold text-[#6F6F6E] text-lg sm:text-xl md:text-2xl px-3">エリア</h3>
          <Button
            className="w-full sm:w-auto sm:min-w-[180px] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-base sm:text-lg"
            onClick={() => navigate('/admin-store/area-setting/area')}
          >
            エリアの作成
          </Button>
        </div>

        {/* Areas Table - Desktop/Tablet */}
        <div className="sm:block overflow-y-auto mb-8 max-h-[40vh]">
          <Table className="min-w-full bg-white rounded shadow overflow-hidden">
            <TableHeader>
              <TableRow>
                <TableHead className="px-4 py-3 text-left bg-gray-100 text-[#6F6F6E] text-base md:text-lg">エリア名</TableHead>
                <TableHead className="px-4 py-3 bg-gray-100 w-32"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dataAreaSetting?.agxAreas?.map((item, index) => (
                <TableRow key={index} className="border-b hover:bg-gray-50">
                  <TableCell className="px-4 py-3">
                    <div
                      className="text-[#6F6F6E] text-sm md:text-base hover:underline cursor-pointer"
                      onClick={() => navigate(`/admin-store/area-setting/area/${item.agx_areaid}`)}
                    >
                      {item.agxAreaName}
                    </div>
                  </TableCell>
                  <TableCell className="px-4 py-3 text-end">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 md:py-3 md:px-8 rounded transition text-sm md:text-base"
                      onClick={() => handleDeleteArea(item.agx_areaid)}
                    >
                      削除
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* SubArea Section */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 border-b border-[#707070] pb-4 space-y-3 sm:space-y-0">
          <h3 className="font-semibold text-[#6F6F6E] text-lg sm:text-xl md:text-2xl px-3">サブエリア</h3>
          <Button
            className="w-full sm:w-auto sm:min-w-[180px] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-base sm:text-lg"
            onClick={() => navigate('/admin-store/area-setting/sub-area')}
          >
            サブエリアの作成
          </Button>
        </div>

        {/* SubAreas Table - Desktop */}
        <div className="md:block overflow-x-auto mb-8 max-h-[40vh]">
          <Table className="min-w-full bg-white rounded shadow overflow-hidden text-left">
            <TableHeader>
              <TableRow>
                <TableHead className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3 text-nowrap">サブエリア名</TableHead>
                <TableHead className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3 text-nowrap">エリア名</TableHead>
                <TableHead className="px-4 py-3 bg-gray-100 text-[#6F6F6E] text-base md:text-lg w-1/3"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                <TableRow key={index} className="border-b hover:bg-gray-50">
                  <TableCell className="px-4 py-3">
                    <div
                      className="text-[#6F6F6E] text-sm md:text-base hover:underline cursor-pointer"
                      onClick={() => navigate(`/admin-store/area-setting/sub-area/${item.agxSubAreaid}`)}
                    >
                      {item.agxSubAreaName}
                    </div>
                  </TableCell>
                  <TableCell className="px-4 py-3 text-[#6F6F6E] text-sm md:text-base">{item.agxAreaName}</TableCell>
                  <TableCell className="px-4 py-3 text-end">
                    <Button
                      className="bg-[#c94e4e] hover:bg-[#c93838] text-white py-2 px-4 md:py-3 md:px-8 rounded transition text-sm md:text-base"
                      onClick={() => handleDeleteSubArea(item.agxSubAreaid)}
                    >
                      削除
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <ConfirmDeleteModal
          isDialogOpen={isDialogOpen}
          setIsDialogOpen={setIsDialogOpen}
          handleConfirmDelete={handleConfirmDelete}
        />
      </div>
    </div>
  )
}