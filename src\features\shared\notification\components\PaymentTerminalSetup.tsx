import React from 'react';
import { Link } from 'react-router-dom';
import { PaymentTerminalSetupProps } from '@/features/shared/notification/types';

export const PaymentTerminalSetup: React.FC<PaymentTerminalSetupProps> = ({
  appSerialNumber,
  isAdmin = false,
  className = "px-8 mb-4"
}) => {
  return (
    <div className={className}>
      <p className={`text-xl text-[#6F6F6E] mb-2`}>
        アプリシリアルNo. : {appSerialNumber || ''}
      </p>
      <div className="ml-32 space-y-2">
          <div>
            <Link
              to="#"
              className="text-[#1D9987] hover:text-[#1D9987]/80 underline text-base"
            >
              初期設定マニュアルはこちら
            </Link>
          </div>
      </div>
    </div>
  );
};
