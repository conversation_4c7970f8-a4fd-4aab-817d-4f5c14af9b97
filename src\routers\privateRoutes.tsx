 import { Suspense, lazy } from "react"
import { Outlet } from "react-router-dom"
import { RouteConfig } from "@/routers/publicRoutes"
import { AccountTypes, TypeStore } from "@/types/globalType"
import { LoadingSpinner } from "@/components/LoadingSpinner"
import { Layout } from "@/components/layout/layout-private/Layout"
import { ApplicationSteps } from "@/pages/application-steps/Index"
import { Layout as LayoutPublic } from "@/components/layout/layout-public/Layout"
import { RegisterMerchant } from "@/pages/auth/RegisterMerchant"

// eslint-disable-next-line
const AccountPage = lazy(() => import("@/pages/account/Account"));
// eslint-disable-next-line
const Overview = lazy(() => import("@/pages/Overview"));
// eslint-disable-next-line
const ConfigPage = lazy(() => import("@/pages/Config"));
// eslint-disable-next-line
const ConfirmationOfRegisteredStoreDetailsPage = lazy(() => import("@/pages/status/ConfirmationOfRegisteredStoreDetails"));
// eslint-disable-next-line
const NewAccountPage = lazy(() => import("@/pages/new-account/NewAccount"));

// Store
// eslint-disable-next-line
const InvoiceReceipt = lazy(() => import("@/pages/store/invoice-receipt/InvoiceReceipt"));
// eslint-disable-next-line
const StoreNotificationCalendar = lazy(() => import("@/pages/store/notification/calendar/StoreNotificationCalendar"));
// eslint-disable-next-line
const StoreNotificationPaygate = lazy(() => import("@/pages/store/notification/paygate/StoreNotificationPaygate"));
// eslint-disable-next-line
const StoreNotificationFaq = lazy(() => import("@/pages/store/notification/faq/StoreNotificationFaq"));
// eslint-disable-next-line
const StoreNotificationRollpaper = lazy(() => import("@/pages/store/notification/rollpaper/StoreNotificationRollpaper"));
// eslint-disable-next-line
const StoreSupport = lazy(() => import("@/pages/store/support/StoreSupport"));
// eslint-disable-next-line
const StoreDepositPaygate = lazy(() => import("@/pages/store/deposit/paygate/StoreDepositPaygate"));
// eslint-disable-next-line
const StoreDepositDetailAll = lazy(() => import("@/pages/store/deposit/paygate/depositDetailAll/StoreDepositPaygateDetailAll"));
// eslint-disable-next-line
const StoreDepositDetail = lazy(() => import("@/pages/store/deposit/paygate/depositDetail/StoreDepositPaygateDetail"));
// eslint-disable-next-line
const StoreDepositCrepico = lazy(() => import("@/pages/store/deposit/crepico/StoreDepositCrepico"));
// eslint-disable-next-line
const StoreDepositCrepicoDetailAll = lazy(() => import("@/pages/store/deposit/crepico/depositDetailAll/StoreDepositCrepicoDetailAll"));
// eslint-disable-next-line
const StoreDepositCrepicoDetail = lazy(() => import("@/pages/store/deposit/crepico/depositDetail/StoreDepositCrepicoDetail"));
// eslint-disable-next-line
const SummaryCrepico = lazy(() => import("@/pages/store/summary/crepico/SummaryCrepico"));
// eslint-disable-next-line
const SummaryPaygate = lazy(() => import("@/pages/store/summary/paygate/SummaryPaygate"));
// eslint-disable-next-line
const SummaryMonthlyCrepico = lazy(() => import("@/pages/store/summaryMonthly/crepico/SummaryMonthlyCrepico"));
// eslint-disable-next-line
const SummaryMonthlyPaygate = lazy(() => import("@/pages/store/summaryMonthly/paygate/SummaryMonthlyPaygate"));
// eslint-disable-next-line
const StoreDepositMenu = lazy(() => import("@/pages/store/deposit/StoreDepositMenu"));
// eslint-disable-next-line
const Agreement = lazy(() => import("@/pages/store/agreement/Agreement"));
// eslint-disable-next-line
const AgreementPaygate = lazy(() => import("@/pages/store/agreement/paygate/AgreementPaygate"));
// eslint-disable-next-line
const AgreementCrepico = lazy(() => import("@/pages/store/agreement/crepico/AgreementCrepico"));
// eslint-disable-next-line
const StoreCrepicoPayment = lazy(() => import("@/pages/store/crepico-payment/CrepicoPaymentPage"));
// eslint-disable-next-line
const ChangeStoreInformation = lazy(() => import("@/pages/ChangeStoreInformation"));

// Crepico
// eslint-disable-next-line
const InvoiceReceiptCrepico = lazy(() => import("@/pages/store/invoice-receipt/crepico/Crepico"));
// eslint-disable-next-line
const CrepicoInvoice = lazy(() => import("@/pages/store/invoice-receipt/crepico/Invoice"));
// eslint-disable-next-line
const CrepicoInvoiceMonthly = lazy(() => import("@/pages/store/invoice-receipt/crepico/InvoiceMonthly"));
// eslint-disable-next-line
const CrepicoReceiptMonthly = lazy(() => import("@/pages/store/invoice-receipt/crepico/ReceiptMonthly"));
// eslint-disable-next-line
const CrepicoReceipt = lazy(() => import("@/pages/store/invoice-receipt/crepico/Receipt"));

// Paygate
// eslint-disable-next-line
const InvoiceReceiptPaygate = lazy(() => import("@/pages/store/invoice-receipt/paygate/Paygate"));
// eslint-disable-next-line
const PaygateInvoice = lazy(() => import("@/pages/store/invoice-receipt/paygate/Invoice"));
// eslint-disable-next-line
const PaygateInvoiceMonthly = lazy(() => import("@/pages/store/invoice-receipt/paygate/InvoiceMonthly"));
// eslint-disable-next-line
const PaygateReceiptMonthly = lazy(() => import("@/pages/store/invoice-receipt/paygate/ReceiptMonthly"));
// eslint-disable-next-line
const PaygateReceipt = lazy(() => import("@/pages/store/invoice-receipt/paygate/Receipt"));

// Admin Store
// eslint-disable-next-line
const AreaSetting = lazy(() => import("@/pages/area-setting/AreaSetting"));
// eslint-disable-next-line
const AdminNotificationCalendar = lazy(() => import("@/pages/adminStore/notification/calendar/AdminNotificationCalendar"));
// eslint-disable-next-line
const AdminNotificationPaygate = lazy(() => import("@/pages/adminStore/notification/paygate/AdminNotificationPaygate"));
// eslint-disable-next-line
const AdminDeposit = lazy(() => import("@/pages/adminStore/adminDeposit/AdminDeposit"));
// eslint-disable-next-line
const AdminDepositDetail = lazy(() => import("@/pages/adminStore/adminDeposit/adminDepositDetail/AdminDepositDetail"));
// eslint-disable-next-line
const AdminStoreInvoiceReceipt = lazy(() => import("@/pages/adminStore/invoice-receipt/InvoiceReceipt"));
// eslint-disable-next-line
const AdminStoreInvoiceMonthly = lazy(() => import("@/pages/adminStore/invoice-receipt/InvoiceMonthly"));
// eslint-disable-next-line
const AdminStoreReceiptMonthly = lazy(() => import("@/pages/adminStore/invoice-receipt/ReceiptMonthly"));
// eslint-disable-next-line
const CreditCardMonthlyFee = lazy(() => import("@/pages/adminStore/invoice-receipt/CreditCardMonthlyFee"));
// eslint-disable-next-line
const MonthlyCostByStore = lazy(() => import("@/pages/adminStore/invoice-receipt/MonthlyCostByStore"));
// eslint-disable-next-line
const AdminAgreement = lazy(() => import("@/pages/adminStore/agreement/Agreement"));
// eslint-disable-next-line
const AdminStoreCrepicoPayment = lazy(() => import("@/pages/adminStore/crepico-payment/CrepicoPaymentPage"));
// eslint-disable-next-line
const AreaPage = lazy(() => import("@/features/area-setting/components/AreaPage"));
// eslint-disable-next-line
const SubAreaPage = lazy(() => import("@/features/area-setting/components/SubAreaPage"));

const privateRoutes: RouteConfig[] = [

  // Router base
  {
    path: "/",
    element: (
      <Layout>
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet />
        </Suspense>
      </Layout>
    ),
    requiredAccountType: [AccountTypes.STORE, AccountTypes.ADMIN_STORE, AccountTypes.APPLICATION_COMPLETE],
    children: [
      {
        path: "",
        element: <Overview />
      },
      {
        path: "overview",
        element: <Overview />
      },
      {
        path: "account",
        element: <AccountPage />
      },
    ],
  },

  // register merchant
  {
    path: "register-merchant",
    element: (
      <LayoutPublic>
        <RegisterMerchant />
      </LayoutPublic>
    ),
    requiredAccountType: [AccountTypes.APPLICATION],
  },
  {
    path: "application-steps",
    element: (
      <LayoutPublic>
        <ApplicationSteps />
      </LayoutPublic>
    ),
    requiredAccountType: [AccountTypes.APPLICATION_STEPS, AccountTypes.APPLICATION_COMPLETE],
  },

  // new account
  {
    path: "/",
    element: (
      <Layout>
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet />
        </Suspense>
      </Layout>
    ),
    requiredAccountType: [AccountTypes.APPLICATION, AccountTypes.APPLICATION_COMPLETE, AccountTypes.APPLICATION_CANCEL],
    children: [
      {
        path: "new-account",
        element: <NewAccountPage />
      },
      {
        path: "registered-store-details",
        element: <ConfirmationOfRegisteredStoreDetailsPage />
      },
    ],
  },

  // config
  // {
  //   path: "store/config",
  //   element: (
  //     <Layout>
  //       <Suspense fallback={<LoadingSpinner />}>
  //         <ConfigPage />
  //       </Suspense>
  //     </Layout>
  //   ),
  //   requiredAccountType: [AccountTypes.STORE, AccountTypes.APPLICATION_COMPLETE],
  // },

  // Store
  {
    path: "store",
    element: (
      <Layout>
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet />
        </Suspense>
      </Layout>
    ),
    requiredAccountType: [AccountTypes.STORE, AccountTypes.APPLICATION_COMPLETE],
    children: [
      {
        path: "invoice-receipt",
        element: <InvoiceReceipt />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE],
      },
      {
        path: "invoice-receipt/crepico",
        element: <InvoiceReceiptCrepico />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_CREPICO]
      },
      {
        path: "invoice-receipt/crepico/invoice/:invoiceNo",
        element: <CrepicoInvoice />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_CREPICO]
      },
      {
        path: "invoice-receipt/crepico/invoice-monthly/:invoiceNo",
        element: <CrepicoInvoiceMonthly />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_CREPICO]
      },
      {
        path: "invoice-receipt/crepico/receipt/:invoiceNo",
        element: <CrepicoReceipt />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_CREPICO]
      },
      {
        path: "invoice-receipt/crepico/receipt-monthly/:invoiceNo",
        element: <CrepicoReceiptMonthly />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_CREPICO]
      },
      {
        path: "invoice-receipt/paygate",
        element: <InvoiceReceiptPaygate />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "invoice-receipt/paygate/invoice/:invoiceNo",
        element: <PaygateInvoice />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "invoice-receipt/paygate/invoice-monthly/:invoiceNo",
        element: <PaygateInvoiceMonthly />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "invoice-receipt/paygate/receipt/:invoiceNo",
        element: <PaygateReceipt />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "invoice-receipt/paygate/receipt-monthly/:invoiceNo",
        element: <PaygateReceiptMonthly />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "config",
        element: <ConfigPage />
      },
      {
        path: "change-store-information",
        element: <ChangeStoreInformation />
      },
      {
        path: "notification/calendar",
        element: <StoreNotificationCalendar />,
      },
      {
        path: "notification/paygate",
        element: <StoreNotificationPaygate />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE, TypeStore.STORE_PAYGATE]
      },
      {
        path: "notification/faq",
        element: <StoreNotificationFaq />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
      {
        path: "notification/rollpaper",
        element: <StoreNotificationRollpaper />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
      {
        path: "support",
        element: <StoreSupport />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit",
        element: <StoreDepositMenu />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/paygate",
        element: <StoreDepositPaygate />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/paygate/detail/:transferDate",
        element: <StoreDepositDetailAll />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/paygate/detail/:merchantNo/:paymentBId/:transactionType/:transferDate",
        element: <StoreDepositDetail />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/crepico",
        element: <StoreDepositCrepico />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/crepico/:merchantNo",
        element: <StoreDepositCrepico />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/crepico/detail/:transferDate",
        element: <StoreDepositCrepicoDetailAll />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "deposit/crepico/detail/:merchantNo/:paymentBId/:transactionType/:transferDate",
        element: <StoreDepositCrepicoDetail />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "summary/crepico",
        element: <SummaryCrepico />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "summary/paygate",
        element: <SummaryPaygate />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "summary-monthly/crepico",
        element: <SummaryMonthlyCrepico />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "summary-monthly/paygate",
        element: <SummaryMonthlyPaygate />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "agreement",
        element: <Agreement />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
      {
        path: "agreement/paygate",
        element: <AgreementPaygate />,
        requiredTypeStore: [TypeStore.STORE_PAYGATE, TypeStore.STORE_MIGRATE]
      },
      {
        path: "agreement/crepico",
        element: <AgreementCrepico />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      },
      {
        path: "crepico-payment",
        element: <StoreCrepicoPayment />,
        requiredTypeStore: [TypeStore.STORE_CREPICO, TypeStore.STORE_MIGRATE]
      }
    ]
  },

  // Admin Store
  {
    path: "admin-store",
    element: (
      <Layout>
        <Suspense fallback={<LoadingSpinner />}>
          <Outlet />
        </Suspense>
      </Layout>
    ),
    requiredAccountType: [AccountTypes.ADMIN_STORE],
    children: [
      {
        path: "area-setting",
        element: <AreaSetting />
      },
      {
        path: "area-setting/area",
        element: <AreaPage />
      },
      {
        path: "area-setting/area/:id",
        element: <AreaPage />
      },
      {
        path: "area-setting/sub-area",
        element: <SubAreaPage />
      },
      {
        path: "area-setting/sub-area/:id",
        element: <SubAreaPage />
      },
      {
        path: "deposit",
        element: <AdminDeposit />
      },
      {
        path: "deposit/detail/:transferDate",
        element: <AdminDepositDetail />
      },
      {
        path: "deposit/detail/:transferDate/:transactionType/:merchantNo/:paymentBId",
        element: <AdminDepositDetail />,
      },
      {
        path: "notification/calendar",
        element: <AdminNotificationCalendar />
      },
      {
        path: "notification/paygate",
        element: <AdminNotificationPaygate />
      },
      {
        path: "invoice-receipt",
        element: <AdminStoreInvoiceReceipt />
      },
      {
        path: "invoice-receipt/admin-invoice-monthly/:yearMonth",
        element: <AdminStoreInvoiceMonthly />
      },
      {
        path: "invoice-receipt/admin-receipt-monthly/:yearMonth",
        element: <AdminStoreReceiptMonthly />
      },
      {
        path: "admin-invoice-receipt/admin-invoice-monthly/credit-card-monthly-fee/:month/:type",
        element: <CreditCardMonthlyFee />
      },
      {
        path: "admin-invoice-receipt/admin-invoice-monthly/monthly-cost-by-store/:month/:merchantNo",
        element: <MonthlyCostByStore />
      },
      {
        path: "agreement",
        element: <AdminAgreement />
      },
      {
        path: "crepico-payment",
        element: <AdminStoreCrepicoPayment />
      },
      {
        path: "support",
        element: <StoreSupport />,
        requiredTypeStore: [TypeStore.STORE_MIGRATE]
      },
    ]
  },
];


export default privateRoutes
