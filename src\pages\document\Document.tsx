/* eslint-disable no-control-regex */
/* eslint-disable no-useless-escape */
import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useForm, SubmitHandler } from "react-hook-form";
import icoPagetop01 from "/images/ico_pagetop01.png";
import { useQueryAdAddress } from "@/features/document/hooks/useQueryAdAddress";
import { useDocumentDownloadMutation } from "@/features/document/hooks/useDocumentDownloadMutation";
import { Button } from "@/components/ui/button";

interface FormData {
  agxName: string;
  agxEmail: string;
  agxEmailConfirm: string;
  agxReferrals: string;
  agxHowDidYouKnow: string;
  agxBusinessType: string;
  agxStoreName: string;
  agxPhone: string;
  agxCompanyName: string;
  agxCorporatePostalCode: string;
  agxCorporatePrefecture: string;
  agxCorporateAddress1: string;
}

const mapReasonType = new Map([
  [*********, "web検索"],
  [*********, "紹介"],
  [*********, "その他"],
]);

const mapAgxBusinessType = new Map([
  [*********, "クリニック"],
  [*********, "薬局"],
  [*********, "歯科"],
]);

const prefectures = [
  "北海道",
  "青森県",
  "岩手県",
  "宮城県",
  "秋田県",
  "山形県",
  "福島県",
  "茨城県",
  "栃木県",
  "群馬県",
  "埼玉県",
  "千葉県",
  "東京都",
  "神奈川県",
  "新潟県",
  "富山県",
  "石川県",
  "福井県",
  "山梨県",
  "長野県",
  "岐阜県",
  "静岡県",
  "愛知県",
  "三重県",
  "滋賀県",
  "京都府",
  "大阪府",
  "兵庫県",
  "奈良県",
  "和歌山県",
  "鳥取県",
  "島根県",
  "岡山県",
  "広島県",
  "山口県",
  "徳島県",
  "香川県",
  "愛媛県",
  "高知県",
  "福岡県",
  "佐賀県",
  "長崎県",
  "熊本県",
  "大分県",
  "宮崎県",
  "鹿児島県",
  "沖縄県",
];

const Document: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<"input" | "confirm">("input");
  const [agree, setAgree] = useState(true);
  const [isBacking, setIsBacking] = useState(false);
  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
    trigger,
  } = useForm<FormData>({
    defaultValues: {
      agxName: "",
      agxEmail: "",
      agxEmailConfirm: "",
      agxReferrals: "",
      agxHowDidYouKnow: "",
      agxBusinessType: "",
      agxStoreName: "",
      agxPhone: "",
      agxCompanyName: "",
      agxCorporatePostalCode: "",
      agxCorporatePrefecture: "北海道",
      agxCorporateAddress1: "",
    },
  });

  // Chỉ watch khi cần thiết (trong bước confirm hoặc khi cần validate)
  const agxEmail = watch("agxEmail");
  const agxCorporatePostalCode =
    step === "input" ? watch("agxCorporatePostalCode") : "";

  const {
    refetch,
    isFetching,
    error: addressError,
  } = useQueryAdAddress(agxCorporatePostalCode);

  const handleFindAddress = async () => {
    try {
      const { data } = await refetch();
      if (data?.data) {
        setValue("agxCorporatePrefecture", data.data.kenName);
        setValue("agxCorporateAddress1", data.data.cityTownName);
        await trigger(["agxCorporatePrefecture", "agxCorporateAddress1"]);
      } else {
        setValue("agxCorporatePrefecture", "北海道");
        setValue("agxCorporateAddress1", "");
        await trigger(["agxCorporatePrefecture", "agxCorporateAddress1"]);
      }
    } catch (error) {
      console.error("Address fetch error:", error);
    }
  };

  const { downloadDocument, isLoading: isSubmitting } =
    useDocumentDownloadMutation({
      onSuccess: (response) => {
        if (response.data) {
          navigate(
            watch("agxBusinessType") === "*********"
              ? "/document-completed/2"
              : "/document-completed/1"
          );
        }
      },
      onError: (error) => {
        console.error("Submission error:", error);
      },
    });

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (isBacking) {
      return; // Ngăn onSubmit khi đang quay lại
    }
    if (step === "input") {
      setStep("confirm");
    } else if (step === "confirm") {
      await downloadDocument(data);
    }
  };

  const handleBack = () => {
    setIsBacking(true); // Đặt flag để ngăn onSubmit
    setStep("input");
    // Reset flag sau một khoảng thời gian ngắn
    setTimeout(() => setIsBacking(false), 0);
  };

  const handlePostalCodeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    let value = e.target.value;
    if (!value.includes("-") && value.length > 3) {
      value = `${value.substring(0, 3)}-${value.substring(3)}`;
      setValue("agxCorporatePostalCode", value);
    }
  };

  return (
    <div className="relative mb-[83px]">
      <div className="max-w-[1000px] mx-auto py-6 px-4">
        <div className="text-center">
          <h2 className="text-[37px] font-bold mb-9 text-[#727272]">
            お問い合わせフォーム
          </h2>
          <p className="text-sm font-[600] mb-6 text-[#505050] leading-[2]">
            チョキペイの費用や詳しい機能、導入までの流れを掲載した資料をダウンロードいただけます。
            <br />
            以下のフォームにお客様情報を入力後、個人情報の取扱に同意いただき「確認画面へ」を押してください。
          </p>
          <div className="flex justify-center">
            <ul className="flex justify-between gap-2 w-[370px] my-[80px]">
              <li className="flex flex-col items-center text-[15px] text-[#14AEA9] font-[700] whitespace-nowrap gap-2">
                <span
                  className={`w-5 h-5 rounded-full ${
                    step === "input"
                      ? "bg-[#14AEA9]"
                      : "bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px]"
                  }`}
                ></span>
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${
                    step === "input" ? "text-[#14AEA9]" : "text-[#a0a0a0]"
                  }`}
                >
                  入力
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex flex-col text-[15px] text-[#14AEA9] font-[700] items-center whitespace-nowrap gap-2">
                <span
                  className={`w-5 h-5 rounded-full ${
                    step === "confirm"
                      ? "bg-[#14AEA9]"
                      : "bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px]"
                  }`}
                ></span>
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${
                    step === "confirm" ? "text-[#14AEA9]" : "text-[#a0a0a0]"
                  }`}
                >
                  確認
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex flex-col items-center text-[15px] text-[#a0a0a0] font-[700] whitespace-nowrap gap-2">
                <span className=" bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px] rounded-full"></span>
                完了
              </li>
            </ul>
          </div>
        </div>

        <form
          onSubmit={handleSubmit(onSubmit)}
          onKeyDown={(e) => {
            if (e.key === "Enter") e.preventDefault();
          }}
          className="bg-[#fafafa] p-6 rounded-lg shadow-sm"
        >
          <table className="w-full space-y-5 md:space-y-0">
            <tbody>
              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お名前{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxName")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxName", {
                          required: "お名前を入力してください。",
                        })}
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    Email{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxEmail")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxEmail", {
                          required: "有効なEmailを入力してください。",
                          pattern: {
                            value:
                              /^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/,
                            message: "有効なEmailを入力してください。",
                          },
                        })}
                        type="email"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxEmail && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxEmail.message}
                        </p>
                      )}
                      <div className="mt-3">
                        <p className="text-md font-semibold text-[#333333]">
                          確認用
                        </p>
                        <input
                          {...register("agxEmailConfirm", {
                            required: "確認用Emailを入力してください。",
                            validate: (value) =>
                              value === agxEmail || "Emailが一致しません。",
                          })}
                          type="email"
                          className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                        />
                        {errors.agxEmailConfirm && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.agxEmailConfirm.message}
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お電話番号{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxPhone")}
                    </span>
                  ) : (
                    <>
                      <div className="flex flex-col 2xl:flex-row 2xl:items-center 2xl:gap-3">
                        <input
                          {...register("agxPhone", {
                            required: "有効なお電話番号を入力してください。",
                            pattern: {
                              value: /^[\d\-\(\) ]*$/,
                              message: "有効なお電話番号を入力してください。",
                            },
                          })}
                          type="tel"
                          className="mt-1 2xl:mt-0 block w-full 2xl:flex-1 rounded-md border border-gray-300 bg-white p-2 "
                        />
                        <span className="mt-2 2xl:mt-0 whitespace-nowrap font-semibold text-gray-600">
                          半角英数
                        </span>
                      </div>
                      {errors.agxPhone && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxPhone.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    業種{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {mapAgxBusinessType.get(Number(watch("agxBusinessType")))}
                    </span>
                  ) : (
                    <>
                      <select
                        {...register("agxBusinessType", {
                          required: "業種を選択してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      >
                        <option value="">選択してください</option>
                        <option value="*********">クリニック</option>
                        <option value="*********">薬局</option>
                        <option value="*********">歯科</option>
                      </select>
                      {errors.agxBusinessType && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxBusinessType.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    郵便番号{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxCorporatePostalCode")}
                    </span>
                  ) : (
                    <>
                      <div className="flex h-9">
                        <span className="flex items-center px-3 border border-r-0 border-gray-300 bg-gray-200 text-gray-600">
                          〒
                        </span>
                        <input
                          {...register("agxCorporatePostalCode", {
                            required: "郵便番号を入力してください。",
                            pattern: {
                              value: /^(\d{7}|\d{3}-\d{4})$/,
                              message: "書式が不正です。",
                            },
                          })}
                          type="text"
                          maxLength={8}
                          placeholder="郵便番号を入力してください。"
                          className=" border border-gray-300 bg-white p-2 focus:border-teal-500 focus:ring-teal-500"
                          onChange={(e) => {
                            register("agxCorporatePostalCode").onChange(e);
                            handlePostalCodeChange(e);
                          }}
                        />
                        <Button
                          type="button"
                          onClick={handleFindAddress}
                          disabled={isFetching}
                          className="px-3 h-full py-2 bg-[#17a2b8] text-white rounded-none hover:bg-[#17a2b8] disabled:bg-gray-400 disabled:cursor-not-allowed"
                        >
                          {isFetching ? "検索中..." : "検索"}
                        </Button>
                      </div>
                      {errors.agxCorporatePostalCode && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxCorporatePostalCode.message}
                        </p>
                      )}
                      {addressError && (
                        <p className="mt-1 text-sm text-red-600">
                          住所の取得に失敗しました。もう一度お試しください。
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    都道府県{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxCorporatePrefecture")}
                    </span>
                  ) : (
                    <>
                      <select
                        {...register("agxCorporatePrefecture", {
                          required: "都道府県を選択してください。",
                        })}
                        className="mt-1 block w-full md:w-1/3 rounded-md border border-gray-300 bg-white p-2 "
                      >
                        {prefectures.map((pref) => (
                          <option key={pref} value={pref}>
                            {pref}
                          </option>
                        ))}
                      </select>
                      {errors.agxCorporatePrefecture && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxCorporatePrefecture.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    住所１{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxCorporateAddress1")}
                    </span>
                  ) : (
                    <>
                      <div className="flex flex-col md:flex-row 2xl:items-center 2xl:gap-3">
                        <input
                          {...register("agxCorporateAddress1", {
                            required: "住所を入力してください。",
                            validate: (value) =>
                              /[^\x01-\x7E]/.test(value) ||
                              "全角文字で入力してください。",
                          })}
                          type="text"
                          maxLength={100}
                          className="mt-1 2xl:mt-0 block w-full md:flex-1 rounded-md border border-gray-300 bg-white p-2 "
                        />
                        <span className="mt-2 font-semibold text-gray-600">（全角）</span>
                      </div>
                      {errors.agxCorporateAddress1 && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxCorporateAddress1.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    法人名
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxCompanyName")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxCompanyName", {
                          required: "法人名を入力してください。",
                        })}
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxCompanyName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxCompanyName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    店舗名{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxStoreName")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxStoreName", {
                          required: "店舗名を入力してください。",
                        })}
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxStoreName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxStoreName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    チョキペイを知ったきっかけ{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {mapReasonType.get(Number(watch("agxHowDidYouKnow")))}
                    </span>
                  ) : (
                    <>
                      <select
                        {...register("agxHowDidYouKnow", {
                          required: "お問い合わせ内容を選択してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      >
                        <option value="">選択してください</option>
                        <option value="*********">web検索</option>
                        <option value="*********">紹介</option>
                        <option value="*********">その他</option>
                      </select>
                      {errors.agxHowDidYouKnow && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxHowDidYouKnow.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    具体的な媒体、紹介先、その他の概要{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <pre className="bg-white p-2 mt-1 text-gray-800">
                      {watch("agxReferrals")}
                    </pre>
                  ) : (
                    <>
                      <textarea
                        {...register("agxReferrals", {
                          required:
                            "具体的な媒体、紹介先、その他の概要を入力してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxReferrals && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxReferrals.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <td colSpan={2} className="block md:table-cell w-full p-0 md:p-5">
                  <div className="flex justify-center w-full flex-col">
                    {step == "input" && (
                      <div className="flex items-center justify-center flex-col mb-4 gap-[25px]">
                        <div
                          className="checkbox"
                          style={{ marginBottom: "5px", marginTop: "0px" }}
                        >
                          <label
                            id="personal_information_lable"
                            style={{ paddingLeft: "3px" }}
                          >
                            <input
                              className="hidden"
                              type="checkbox"
                              name="agree"
                              checked={agree}
                              onChange={() => setAgree(!agree)}
                            />
                            <span>当社の個人情報保護方針に同意する</span>
                          </label>
                        </div>
                      </div>
                    )}
                    <p className="flex items-center justify-center mb-4 text-sm font-semibold text-[#505050]">
                      ※ ｢
                      <a
                        className="text-[#13ae9c] hover:text-[#13ae9c]/80  border-b border-[#505050]"
                        href="https://choqi.co.jp/privacypolicy.html"
                        target="_blank"
                      >
                        個人情報保護方針
                      </a>
                      ｣について別ウインドウで開く
                    </p>
                    <div className="w-full pb-[65px]">
                      {step === "confirm" ? (
                        <div className="flex justify-center gap-4 mt-6">
                          <button
                            type="button"
                            onClick={handleBack}
                            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-full hover:bg-gray-100 w-[200px] md:w-[240px] flex justify-center items-center"
                          >
                            戻る
                          </button>
                          <div>
                            <ul className="btn01 greenBtn01 centerBtn01">
                              <li
                                style={{
                                  listStyleType: "none",
                                  marginRight: "50%",
                                }}
                              >
                                <button
                                  className="btn01 greenBtn01 centerBtn01"
                                  id="btnNext"
                                  disabled={!agree}
                                >
                                  <span>
                                    {isSubmitting ? "送信中..." : "送信"}
                                  </span>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      ) : (
                        <div className="flex justify-center mt-6">
                          <div>
                            <ul className="btn01 greenBtn01 centerBtn01">
                              <li
                                style={{
                                  listStyleType: "none",
                                  marginRight: "50%",
                                }}
                              >
                                <button
                                  type="submit"
                                  className="btn01 greenBtn01 centerBtn01"
                                  id="btnNext"
                                  disabled={!agree}
                                >
                                  <span>確認画面へ</span>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </form>
      </div>
      <div>
        <div className="flex justify-end mt-[100px] mr-[20px] mb-5">
          <p
            className=" bg-white rounded-full shadow-lg cursor-pointer"
            onClick={() => {
              window.scrollTo({ top: 0 });
            }}
          >
            <img src={icoPagetop01} alt="pagetop" />
          </p>
        </div>
        <div className="w-full flex justify-center bg-[#EEEEEE] py-4 ">
          <ul className="contactContent01 flex space-x-4 list-none">
            <li className="mailBtn01 text-blue-700">
              <Link to="/contact">メールで相談</Link>
            </li>
            <li className="greenBtn01 btn01 downloadBtn01">
              <Link
                to="/document"
                className="!no-underline hover:text-blue-700 font-semibold flex items-center"
              >
                <p className="z-[999]">資料ダウンロード</p>
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Document;
