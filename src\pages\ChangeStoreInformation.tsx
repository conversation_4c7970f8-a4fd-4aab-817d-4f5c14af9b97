import exclamationPointIcon from "@/assets/images/グループ 1351.svg";
import { Card, CardContent } from "@/components/ui/card";
import { useAuthStore } from "@/store";
import { TypeStore } from "@/types/globalType";

const Notification = () => {
  return (
    <>
      <div className="flex">
        <img src={exclamationPointIcon} alt="" />
        <h2 className="text-xl md:text-2xl text-[#6F6F6E]">お知らせ</h2>
      </div>
      <div className="min-h-[130px] mt-4 w-full max-w-full xl:max-w-[87%] lg:max-w-[1373px] border border-gray-500 rounded-lg bg-white shadow-md">
        <div className="p-4 md:p-4 lg:pl-10">
          <div className="flex items-start">
            <div className="">
              <ul className="py-0 text-[#6F6F6E] list-disc pl-5">    
                <li>
                  <div className="block items-center space-x-2 font-normal">
                    <span className="text-[18px] md:text-[20px] lg:text-[22px]">変更が発生した場合は、下記変更フォームより変更内容を申請してください。</span>
                    <br />
                    <span className="text-[18px] md:text-[20px] lg:text-[22px] !ml-0">チョキペイサポートデスクにて変更内容を確認後、手続きを進めます。</span>
                  </div>
                </li>
                <li>
                  <div className="block items-center space-x-2 font-normal py-2">
                    <span className="text-[18px] md:text-[20px] lg:text-[22px]">変更内容によっては、別途お手続きが必要な場合がございます。その際はチョキペイサポートデスクよりご案内致します。</span>
                  </div>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </>
  )
}

const Cards = () => {
  const { typeStore } = useAuthStore();

  const cardData = [
    { title: "店舗情報変更", url: "https://share.hsforms.com/1eOeTAcdlScalXkXpwBxo1gbyxau" },
    { title: "法人情報変更", url: "https://share.hsforms.com/1o00cZRneRnG2N8KH4CJj2Qbyxau" },
    { title: "代表者情報変更", url: "https://www.jcb.co.jp/kiyaku/pdf/kameiten0705_05.pdf" },
    { title: "口座情報変更", url: "https://share.hsforms.com/1SM5oChf5QNOQykLjXY7kHAbyxau" },
    { title: "担当者情報変更", url: "https://share.hsforms.com/1fofjI2VvR4G-DK5BP-CdgQbyxau" },
    { title: "決済種別変更", url: typeStore === TypeStore.STORE_CREPICO ? "https://share.hsforms.com/1Zv33cqFCR42eXQSd5KtTCAbyxau" : "https://share.hsforms.com/1Jn9CSMLDSe6DWEF519wJzQbyxau" }
  ];

  const handleClickCard = (url: string) => {
    window.open(url, "_blank");
  }

  return (
    <div className="pt-16 xl:pt-24 xl:pr-52">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 md:gap-8">
        {cardData.map((item, index) => (
          <Card 
            key={index}
            className="cursor-pointer transition-all duration-200 border border-gray-500 shadow-md mb-8"
            onClick={() => handleClickCard(item.url)}
          >
            <CardContent className="p-4 flex items-center justify-center min-h-[80px]">
              <h3 className="text-[20px] font-medium text-center text-gray-500">{item.title}</h3>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

const ChangeStoreInformation = () => {

  return (
    <div className="min-h-[calc(100vh-240px)] p-4 md:py-6 md:px-1 xl:p-6 text-[20px]">
      <div className="md:flex items-center md:justify-between pb-4 xl:pr-32">
        <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#6F6F6E]">
          加盟店情報の変更ができます
        </h2>
        <h2 className="text-[20px] md:text-[22px] lg:text-[24px] text-[#1D9987] cursor-pointer underline underline-offset-4 decoration-[#1D9987]/80"
          onClick={() => window.open("/contact")} 
        >
          問い合わせフォーム
        </h2>
      </div>
      <hr className="border-gray-500 border-1" />
      <div className="pt-4">
        <Notification />
        <Cards />
      </div>
    </div>
  );
}

export default ChangeStoreInformation;
