import React from 'react';
import { Link } from 'react-router-dom';
import { TerminalManagementProps } from '../types';

export const TerminalManagement: React.FC<TerminalManagementProps> = ({ 
  id, 
  password, 
  isAdmin = false, 
  className = "" 
}) => {
  return (
    <div className={className}>
      <div className="space-y-2 mb-4 px-8">
        <p className={`text-[20px] text-[#6F6F6E]`}>
          ログインID : {id || ''}
        </p>
        <p className={`text-[20px] text-[#6F6F6E]`}>
          初回パスワード : {password || ''}
        </p>
      </div>
      <div className="ml-8 mb-4 space-y-2">
          <div>
            <Link
              to="https://member.paygate.ne.jp/login"
              target="_blank"
              rel="noopener noreferrer"
              className="text-[#1D9987] hover:text-[#1D9987]/80 underline text-base"
            >
              管理画面はこちらから
            </Link>
            <span className="text-base text-[#6F6F6E] ml-2">
              | 端末管理画面は日々の決済情報の確認等ができます。
            </span>
          </div>
      </div>
    </div>
  );
};
