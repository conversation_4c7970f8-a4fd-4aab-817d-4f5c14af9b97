import React, { useEffect } from 'react';
import { useCrepicoPayment } from '../hooks/useCrepicoPayment';
import { SearchForm } from './SearchForm';
import { PaymentTable } from './PaymentTable';
import {
    TransactionCode,
    TransactionType,
    TransactioCategory
} from '@/constants/common.constant';

export const CrepicoPaymentPage: React.FC = () => {
    const {
        // State
        isInitialLoad,
        setIsInitialLoad,
        page,
        setPage,
        paymentStatus,
        paymentTypes,
        transactionCategory,
        paymentTimeFrom,
        paymentTimeTo,
        saleAmountFrom,
        saleAmountTo,
        terminalIdentification,
        payments,
        error,
        sortDirectionByValue,
        agxNewTerminalNos,
        csvLinkRef,

        // Computed values
        agxStoreName,
        csvExport,
        isAdminStore,

        // Functions
        handlePaymentStatus,
        handleTransactionCategory,
        handlePaymentTypes,
        handleTerminalIdentification,
        handlePaymentTimeFrom,
        handlePaymentTimeTo,
        handleSaleAmountFrom,
        handleSaleAmountTo,
        handleSubmitSearch,
        handleSort,
        handleExportData,
        sortValue,
        sortDirection
    } = useCrepicoPayment();

    useEffect(() => {
        if (!isInitialLoad) {
            handleSubmitSearch();
        } else {
            setIsInitialLoad(false);
        }
    }, [page, sortValue, sortDirection]);

    return (
        <div>
            <div className='px-2 xl:px-6 mt-6 border-b border-[#6F6F6E] pb-6'>
                <h1 className='text-2xl text-[#6F6F6E]'>決済状況、決済種別、決済日時、売上金額を指定のうえ検索できます</h1>
            </div>
            <div className='px-2 xl:px-14 pt-10 text-[#6F6F6E] text-2xl'>
                <SearchForm
                    agxStoreName={agxStoreName}
                    agxNewTerminalNos={agxNewTerminalNos}
                    terminalIdentification={terminalIdentification}
                    paymentStatus={paymentStatus}
                    paymentTypes={paymentTypes}
                    transactionCategory={transactionCategory}
                    paymentTimeFrom={paymentTimeFrom}
                    paymentTimeTo={paymentTimeTo}
                    saleAmountFrom={saleAmountFrom}
                    saleAmountTo={saleAmountTo}
                    error={error}
                    TransactionCode={TransactionCode}
                    TransactioCategory={TransactioCategory}
                    TransactionType={TransactionType}
                    onTerminalIdentificationChange={handleTerminalIdentification}
                    onPaymentStatusChange={handlePaymentStatus}
                    onTransactionCategoryChange={handleTransactionCategory}
                    onPaymentTypesChange={handlePaymentTypes}
                    onPaymentTimeFromChange={handlePaymentTimeFrom}
                    onPaymentTimeToChange={handlePaymentTimeTo}
                    onSaleAmountFromChange={handleSaleAmountFrom}
                    onSaleAmountToChange={handleSaleAmountTo}
                    onSubmitSearch={handleSubmitSearch}
                    isAdminStore={isAdminStore}
                />
                
                {payments && payments.data && payments.data.data && payments.data.data.length > 0 ? (
                    <PaymentTable
                        payments={payments}
                        page={page}
                        setPage={setPage}
                        sortDirectionByValue={sortDirectionByValue}
                        handleSort={handleSort}
                        handleExportData={handleExportData}
                        csvExport={csvExport}
                        csvLinkRef={csvLinkRef}
                    />
                ) : payments !== null ? (
                    <div className="bg-white my-20 px-auto  text-center">
                        <p className="text-2xl text-[#6F6F6E]">データなし</p>
                    </div>
                ) : null}
            </div>
        </div>
    );
};
