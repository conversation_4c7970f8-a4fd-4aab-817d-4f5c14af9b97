import { useAuthStore } from '@/store';
import _ from 'lodash';
import React, { useEffect, useState } from 'react'
import { useQueryAgxArea } from '../hooks/useQueryAgxArea';
import { useCreateAgxArea } from '../hooks/useCreateAgxArea';
import { Button } from '@/components/ui/button';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { useNavigate, useParams } from 'react-router-dom';
import { Input } from '@/components/ui/input';

const initalState = {
    agxAreas: [],
    agxSubAreas: [],
    agxSubAreaModal: [],
    loading: true,
    error: false
}

export const AreaPage = () => {

    const { user } = useAuthStore();
    const navigate = useNavigate();
    const { id } = useParams();
    const isEditMode = Boolean(id);

    const [agxArea, setAgxArea] = useState({
        agx_areaid: null,
        agxMerchantCreatedNo: user?.agxMerchantNo || "",
        agxAreaName: '',
        agxSubAreaids: []
    })

    const [dataAreaSetting, setDataAreaSetting] = useState(initalState);

    const { createAgxAreaAsync, isLoading: isLoadingCreateArea } = useCreateAgxArea();

    const { data: GetAreaSettingResponse, isLoading } = useQueryAgxArea({
        agxMerchantNo: user?.agxMerchantNo || "",
    })

    const handleChangeArea = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.trim();
        setAgxArea({ ...agxArea, agxAreaName: value })
    }

    const handleCreateOrUpdateArea = async () => {
        try {
            await createAgxAreaAsync(agxArea);
            navigate('/admin-store/area-setting');
        } catch (error) {
            console.error('Error creating area:', error);
        }
    }

    // Function to load data area need edit
    const loadAreaForEdit = (areaId: string) => {
        if (GetAreaSettingResponse?.agxAreas) {
            const areaToEdit = GetAreaSettingResponse.agxAreas.find(
                (area: any) => area.agx_areaid === areaId
            );

            // find all sub areas belong to this area
            const subAreasForThisArea = GetAreaSettingResponse.agxSubAreas?.filter(
                (subArea: any) => subArea.agxAreaid === areaId
            ) || [];

            // create array agxSubAreaids from subAreasForThisArea
            const agxSubAreaids = subAreasForThisArea.map((subArea: any) => ({
                agxSubAreaid: subArea.agxSubAreaid,
                isSelected: true,
                isHadArea: true
            }));

            if (areaToEdit) {
                setAgxArea({
                    agx_areaid: areaToEdit.agx_areaid,
                    agxMerchantCreatedNo: user?.agxMerchantNo || "",
                    agxAreaName: areaToEdit.agxAreaName || '',
                    agxSubAreaids: agxSubAreaids
                });
            }
        }
    }

    useEffect(() => {
        getDataAreaSetting();
    }, [GetAreaSettingResponse])

    // Effect để load dữ liệu khi có id trong params
    useEffect(() => {
        if (isEditMode && id && GetAreaSettingResponse) {
            loadAreaForEdit(id);
        }
    }, [isEditMode, id, GetAreaSettingResponse])

    const getDataAreaSetting = async () => {
        try {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: GetAreaSettingResponse.agxAreas,
                agxSubAreas: GetAreaSettingResponse.agxSubAreas,
                agxSubAreaModal: GetAreaSettingResponse.agxSubAreaModal,
                loading: false,
                error: false
            });
        } catch (error) {
            setDataAreaSetting({
                ...dataAreaSetting,
                agxAreas: [],
                agxSubAreas: [],
                agxSubAreaModal: [],
                loading: true,
                error: true
            })
        }
    }

    const handleSelectSubArea = (
        e: React.ChangeEvent<HTMLInputElement>,
        subAreaId: string,
        agxAreaId: string
    ) => {
        const isChecked = e.target.checked;
        const checkbox = document.getElementById(subAreaId) as HTMLInputElement | null;
        if (checkbox) {
            checkbox.checked = isChecked;
        }
        const arrSelected = agxArea.agxSubAreaids || [];
        let updatedSubAreaids: { agxSubAreaid: string; isSelected: boolean; isHadArea: boolean }[];

        if (arrSelected.length > 0) {
            const filteredSubAreaSelected = arrSelected.filter(
                (item: { agxSubAreaid: string }) => item.agxSubAreaid !== subAreaId
            );
            filteredSubAreaSelected.push({
                agxSubAreaid: subAreaId,
                isSelected: isChecked,
                isHadArea: agxAreaId === agxArea.agx_areaid ? true : false,
            });
            updatedSubAreaids = filteredSubAreaSelected;
        } else {
            updatedSubAreaids = [
                {
                    agxSubAreaid: subAreaId,
                    isSelected: true,
                    isHadArea: false,
                },
            ];
        }
        setAgxArea({ ...agxArea, agxSubAreaids: updatedSubAreaids });
    };

    if (isLoading) {
        return <LoadingSpinner />;
    }

    return (
        <div className="pt-6 pb-2 px-2">
            <div className="max-w-full px-2 sm:px-6 md:px-8 lg:px-10 xl:px-28 lg:mt-20 sm:mt-10 mt-4 xl:!pr-[20%]">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between mb-6">
                    <h3 className="mb-2 md:mb-0 text-[#6F6F6E] text-lg sm:text-xl md:text-2xl font-medium">エリアの作成</h3>
                </div>

                {/* Area Name Input - Responsive */}
                <div className="md:ml-10 mb-6 md:px-2 md:flex sm:items-center space-y-2 sm:space-y-0 space-x-2">
                    <label htmlFor="area-name" className="text-[#6F6F6E] mb-2 sm:mb-0 w-[15%] md:w-[25%] lg:w-[15%] md:min-w-[30%] lg:min-w-[20%] text-[20px]">
                        エリアの名称
                    </label>
                    <Input
                        type="text"
                        id="area-name"
                        className="h-[45px] w-full md:min-w-[450px] rounded-[15px] border-gray-300 md:px-3 py-2 text-[#6F6F6E] text-[20px]"
                        value={agxArea.agxAreaName}
                        onChange={handleChangeArea}
                    />
                </div>

                {/* Sub Areas Selection */}
                <div className="md:ml-10 mb-2">
                    <label className="block text-[#6F6F6E] mb-2 text-[20px] px-2">
                    エリアに紐づけするサブエリアの選択
                    </label>
                    
                    <div className="md:block bg-transparent rounded-md overflow-hidden">
                        {/* Fixed Header */}
                        <div className="bg-transparent px-2">
                            <div className="grid grid-cols-[48px_1fr_1fr] gap-0 text-[20px] border-b border-[#6F6F6E]">
                                <div className="md:px-4 py-3 text-center font-medium text-[#6F6F6E]"></div>
                                <div className="md:px-4 py-3 text-center font-medium text-[#6F6F6E]">サブエリア名</div>
                                <div className="md:px-4 py-3 text-center font-medium text-[#6F6F6E]">エリア名</div>
                            </div>
                        </div>

                        {/* Scrollable Body */}
                        <div className="max-h-80 overflow-y-auto">
                            <div className="divide-y divide-gray-200">
                                {dataAreaSetting?.agxSubAreas?.map((item, index) => (
                                    <div key={index} className="grid grid-cols-[48px_1fr_1fr] gap-0 hover:bg-gray-50 transition-colors border-none">
                                        <div className="pr-3 py-4 text-center flex items-center justify-center text-[20px]">
                                            <Input
                                                id={item.agxSubAreaid}
                                                type="checkbox"
                                                className="form-checkbox h-5 w-5 rounded border-gray-300 text-[#1D9987] accent-[#1D9987] cursor-pointer"
                                                checked={!!agxArea.agxSubAreaids?.find((s) => s.agxSubAreaid === item.agxSubAreaid && s.isSelected)}
                                                onChange={(e) => handleSelectSubArea(e, item.agxSubAreaid, item.agxAreaid)}
                                            />
                                        </div>
                                        <div className="px-4 py-3 text-center flex items-center justify-center text-[#6F6F6E] text-[20px]">{item.agxSubAreaName}</div>
                                        <div className="px-4 py-3 text-center flex items-center justify-center text-[#6F6F6E] text-[20px]">{item.agxAreaName}</div>
                                    </div>
                                ))}
                            </div>
                        </div>
                    </div>
                </div>

                {/* Submit Button - Responsive */}
                <div className="flex flex-col pt-10 mb-6 pb-4 items-center sm:items-end justify-center sm:justify-end sm:pr-16">
                    <Button
                        disabled={isLoadingCreateArea}
                        className="w-full md:w-[35%] lg:w-[28%] xl:w-[22%] bg-transparent hover:bg-transparent text-[#707070] font-bold border border-[#707070] px-4 py-2 rounded-md h-[48px] transition-colors disabled:opacity-50 disabled:cursor-not-allowed shadow-md text-lg sm:text-xl"
                        onClick={handleCreateOrUpdateArea}
                    >
                        登錄
                    </Button>
                </div>
            </div>
        </div>
    )
}

export default AreaPage;