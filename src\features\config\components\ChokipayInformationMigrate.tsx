import { Card, CardContent } from "@/components/ui/card";
import { useCallback, useEffect, useRef, useState } from "react";
import { useQueryAgxFeeRate } from "../hooks/useQueryAgxFeeRate";
import { GetMerchantStatusResponse } from "../types";
import { Label } from "@/components/ui/label";
import { formatNumberJP, getTerminalColorName } from "@/utils/helper";
import onedot1dot1 from "/images/group_10.svg";
import onedot1dot2 from "/images/group_12.svg";
import onedot2 from "/images/group_1398.png";
import onedot3dot1 from '/images/group_23.svg';
import onedot3dot2 from '/images/group_25.svg';
import onedot3dot3 from '/images/group_27.png';
import onedot3dot4 from '/images/group_1257.png';
import onedot3dot5 from '/images/group_1259.png';
import twodot1 from '/images/group_1261.svg';
import twodot2 from '/images/group_1263.svg';
import twodot3 from '/images/group_1265.svg';
import twodot4 from '/images/group_1268.svg';
import twodot5 from '/images/group_1270.svg';
import twodot6 from '/images/group_1272.svg';
import twodot7 from '/images/group_1276.png';
import threedot1dot1 from '/images/group_1278.svg';
import threedot1dot2 from '/images/group_1280.svg';
import threedot1dot3 from '/images/group_1282.svg';
import threedot1dot4 from '/images/group_1284.svg';
import threedot1dot5 from '/images/group_1286.svg';
import threedot2dot1 from '/images/group_1288.svg';
import threedot2dot2 from '/images/group_1292.svg';
import threedot2dot3 from '/images/group_1294.svg';
import threedot2dot4 from '/images/group_1296.svg';
import threedot3 from '/images/group_1299.svg';
import threedot4 from '/images/group_1301.svg';
import threedot5 from '/images/group_1308.svg';
import threedot6 from '/images/group_1310.svg';
import threedot7 from '/images/group_1312.svg';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Button } from "@/components/ui/button";



const moneyData = {
  money_Core_Crepico: 99800,
  money_Credit_Card_And_QRcode: 500,
  money_All_Crepico: 1500,
};

interface ChokipayInformationProps {
  merchantData: GetMerchantStatusResponse;
  merchantCrepicoData: GetMerchantStatusResponse;
}

function ChokipayInformationMigrate({ merchantData, merchantCrepicoData }: ChokipayInformationProps) {
  const [initialFee, setInitialFee] = useState("");
  const [monthlyFee, setMonthlyFee] = useState("");
  
  const creditRef = useRef<HTMLDivElement>(null);
  const qrRef = useRef<HTMLDivElement>(null);
  const emoneyRef = useRef<HTMLDivElement>(null);
  const monthlyRef = useRef<HTMLDivElement>(null);

  // Hàm scroll tới ref
  const scrollToRef = (ref: React.RefObject<HTMLDivElement>) => {
    ref.current?.scrollIntoView({ behavior: "smooth", block: "start" });
  };

  const { data: feeRate } = useQueryAgxFeeRate({
    agxBusinessType: merchantData?.agxBusinessType,
  });

  const { data: feeRateCrepico } = useQueryAgxFeeRate({
    agxBusinessType: merchantCrepicoData?.agxBusinessType,
  });
  const caclMoney = useCallback(
    (agxNumberOfTerminal, agxSettlementPackage1, agxSettlementPackage2) =>
      () => {
        const moneyCrepico =
          parseInt(agxNumberOfTerminal) *
          ((agxSettlementPackage1
            ? moneyData.money_Credit_Card_And_QRcode
            : 0) +
            (agxSettlementPackage2 ? moneyData.money_All_Crepico : 0));
        setInitialFee(
          (agxNumberOfTerminal * moneyData.money_Core_Crepico)
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
        setMonthlyFee(
          moneyCrepico
            .toLocaleString("ja-JP", { style: "currency", currency: "JPY" })
            .substring(1)
        );
      },
    []
  );

  const getMoney = (param) => {
    const money = merchantData?.agxNumberOfTerminal * 1 * param * 1;
    const result = money.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
    return result;
  }

  const totalMoney = () => {
    let result = 0;
    const baseMoney = merchantData?.agxNumberOfTerminal * 1 * 400;
    if (merchantData?.agxSettlementTraffic) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementNanaco) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementWaon) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementEdy) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementAid) {
      result += baseMoney;
    }
    if (merchantData?.agxSettlementQuicpay) {
      result += baseMoney;
    }
    return result.toLocaleString("ja-JP", { style: "currency", currency: "JPY" }).substring(1);
  }

  useEffect(() => {
    caclMoney(
      merchantData?.agxNumberOfTerminal,
      merchantData?.agxSettlementPackage1,
      merchantData?.agxSettlementPackage2
    )();
  }, []);
  return (
    <Card className="border-0 shadow-none">
      <CardContent className="py-8 pl-4 space-y-8">
        {/* Thanh menu tab */}
        <div className="flex flex-col items-start md:flex-row gap-2 md:gap-12 mb-1 md:mb-8">
          <Button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition bg-white hover:bg-white"
            onClick={() => scrollToRef(creditRef)}
            type="button"
          >
            クレジットカード
          </Button>
          <Button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition bg-white hover:bg-white"
            onClick={() => scrollToRef(qrRef)}
            type="button"
          >
            QRコード決済
          </Button>
          <Button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition bg-white hover:bg-white"
            onClick={() => scrollToRef(emoneyRef)}
            type="button"
          >
            電子マネー
          </Button>
          <Button
            className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition bg-white hover:bg-white"
            onClick={() => scrollToRef(monthlyRef)}
            type="button"
          >
            月額費用
          </Button>
        </div>

        <div className="grid grid-cols-12 items-center max-md:gap-1 gap-6">
          <div className="col-span-12" ref={creditRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">クレジットカード</div>
            <hr className='mt-2 border-1 border-gray-400' />
            <Table className="w-full overflow-x-auto">
              <TableHeader>
                <TableRow className="border-none hover:bg-white">
                    <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[200px] xl:w-[23%] text-[#6F6F6E] font-medium"></TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[250px] xl:w-[30%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            備考
                          </div>
                        </div>
                    </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className='text-[#6F6F6E]'>
                  <TableRow className="border-0">
                    <TableCell className="flex text-left py-4">
                      <img src={onedot1dot1} alt="Visa" className="mr-2" />
                      <img src={onedot1dot2} alt="Visa" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRate?.creditVisaRate}%
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.creditVisaRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      
                    </TableCell>
                  </TableRow>
                  <TableRow className="border-0">
                    <TableCell className="flex text-left py-4">
                      <img src={onedot2} alt="Visa" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRate?.creditUnionRate}%
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.creditUnionRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={onedot3dot1} alt="two" className="mr-2" />
                      <img src={onedot3dot2} alt="two" className="mr-2" />
                      <img src={onedot3dot3} alt="two" className="mr-2" />
                      <img src={onedot3dot4} alt="two" className="mr-2" />
                      <img src={onedot3dot5} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRate?.creditJcbRate}%
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.creditJcbRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxJcbExistsMembershipFlag  ? "弊社外での契約" : ""}
                    </TableCell>
                  </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="col-span-12"  ref={qrRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">QRコード決済</div>
            <hr className='mt-2 border-1 border-gray-400' />
            <Table className="w-full overflow-x-auto">
              <TableHeader>
                <TableRow className="border-none hover:bg-white">
                    <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[200px] xl:w-[23%] text-[#6F6F6E] font-medium"></TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[250px] xl:w-[30%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            備考
                          </div>
                        </div>
                    </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className='text-[#6F6F6E]'>
                  <TableRow className="border-0">
                    <TableCell className="flex text-left py-4">
                      <img src={twodot1} alt="Visa" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow className="border-0">
                    <TableCell className="flex text-left py-4">
                      <img src={twodot2} alt="Visa" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot1} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot3} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot4} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot5} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot6} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrOtherBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrOtherBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={twodot7} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQrCode ? `${feeRate?.qrBankpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      利用可能
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {feeRateCrepico?.qrBankpayRate}%
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="col-span-12" ref={emoneyRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">電子マネー</div>
            <hr className='mt-2 border-1 border-gray-400' />
            <Table className="w-full overflow-x-auto">
              <TableHeader>
                <TableRow className="border-none hover:bg-white">
                    <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[200px] xl:w-[23%] text-[#6F6F6E] font-medium"></TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[130px] xl:w-[10%] text-[#6F6F6E] font-normal text-[20px]">
                      <div className='w-full flex items-center justify-center'>
                        <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                            ステータス
                        </div>
                      </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:w-[15%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            利用開始日
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[140px] xl:min-w-[140px] xl:w-[13%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            決済手数料率
                          </div>
                        </div>
                    </TableHead>
                    <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[250px] xl:w-[30%] text-[#6F6F6E] font-normal text-[20px]">
                        <div className='w-full flex items-center justify-center'>
                          <div className='flex items-center justify-center py-2 border-b border-gray-400 w-full'>
                            備考
                          </div>
                        </div>
                    </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody className='text-[#6F6F6E]'>
                <TableRow className="border-0">
                  <TableCell className="text-left py-4">
                    <div className="flex flex-wrap items-center">
                      <img src={threedot1dot1} alt="two" className="mr-2 py-1" />
                      <img src={threedot1dot2} alt="two" className="mr-2 py-1" />
                      <img src={threedot1dot3} alt="two" className="mr-2 py-1" />
                      <img src={threedot1dot4} alt="two" className="mr-2 py-1" />
                      <img src={threedot1dot5} alt="two" className="py-1" />
                    </div>
                    <div className="flex flex-wrap items-center">
                      <img src={threedot2dot1} alt="two" className="mr-2 py-1" />
                      <img src={threedot2dot2} alt="two" className="mr-2 py-1" />
                      <img src={threedot2dot3} alt="two" className="mr-2 py-1" />
                      <img src={threedot2dot4} alt="two" className="py-1" />
                    </div>
                  </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementTraffic ? '利用可能' : '未申込'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementTraffic ? `(${getMoney(400)}円)` : '-'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementTraffic ? `${feeRate?.transportationRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRateCrepico?.transportationRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow className="border-0">
                    <TableCell className="flex text-left py-4">
                      <img src={threedot3} alt="waon" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementWaon ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementWaon ? `${feeRate?.waonRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRate?.waonRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={threedot4} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementAid ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementAid ? `${feeRate?.idRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRateCrepico?.idRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={threedot5} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementNanaco ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementNanaco ? `${feeRate?.nanacoRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRateCrepico?.nanacoRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      <a className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition">
                        お申し込みはこちら
                      </a>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={threedot6} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementEdy ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementEdy ? `${feeRate?.edyRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRateCrepico?.edyRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      <a className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition">
                        お申し込みはこちら
                      </a>
                    </TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="flex flex-wrap text-left py-4">
                      <img src={threedot7} alt="two" />
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQuicpay ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantData?.agxSettlementQuicpay ? `${feeRate?.quicpayRate}%` : '-'}
                    </TableCell>

                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? "利用可能" : "未申込"}
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      -
                    </TableCell>
                    <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                      {merchantCrepicoData?.agxSettlementPackage2 ? `${feeRateCrepico?.quicpayRate}%` : '-'}
                    </TableCell>
                    <TableCell className="text-left py-4 px-6 text-[18px] md:text-[20px]">
                      <a className="text-[#1db199] text-[18px] underline underline-offset-4 font-medium hover:opacity-80 transition">
                        お申し込みはこちら
                      </a>
                    </TableCell>
                  </TableRow>
              </TableBody>
            </Table>
          </div>

          <div className="col-span-12" ref={monthlyRef}>
            <div className="col-span-12 text-lg font-bold mt-5 sm:text-[18px] md:text-[20px] text-[24px]  text-[#6F6F6E]">月額費用</div>
            <hr className='mt-2 border-1 border-gray-400' />
            <Table className="w-full xl:w-5/6 overflow-x-auto">
            <TableHeader>
              <TableRow className="border-none hover:bg-white">
                  <TableHead className="text-center py-4 px-1 xl:px-6 min-w-[260px] md:min-w-[150px] lg:min-w-[300px] w-[50%] lg:w-[25%] xl:w-[5%] text-[#6F6F6E] font-medium"></TableHead>
                  <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[150px] md:min-w-[100px] xl:w-[5%] text-[#6F6F6E] font-normal text-[20px]">
                    <div className='w-full flex items-center justify-center'>
                      <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                        税抜金額
                      </div>
                    </div>
                  </TableHead>
                  <TableHead className="text-center py-4 px-1 xl:px-4 min-w-[150px] md:min-w-[100px] xl:w-[5%] text-[#6F6F6E] font-normal text-[20px]">
                    <div className='w-full flex items-center justify-center'>
                      <div className='w-full flex items-center justify-center py-2 border-b border-gray-400'>
                        税抜金額
                      </div>
                    </div>
                  </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className='text-[#6F6F6E]'>
              <TableRow className="border-0">
                <TableCell className="text-left py-4 text-[18px] md:text-[20px]">
                  クレジットカード・QRコード
                </TableCell>
                <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                  {formatNumberJP(getMoney(600))}円
                </TableCell>
                <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                  {formatNumberJP(500)}円
                </TableCell>
              </TableRow>
              <TableRow>
                <TableCell className="text-left py-4 text-[18px] md:text-[20px]">
                  電子マネー
                </TableCell>
                <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                  {totalMoney()}円
                </TableCell>
                <TableCell className="text-center py-4 px-6 text-[18px] md:text-[20px]">
                  {merchantCrepicoData?.agxSettlementPackage2 ? `${formatNumberJP(1000)}円` : '-'}
                </TableCell>
              </TableRow>
            </TableBody>
          </Table>
          </div>
        </div>

        <div className="grid grid-cols-12 items-start max-md:gap-1 gap-6">
          <div className="col-span-12">
            <div className="mt-4 pl-4 text-red-500 text-[18px] sm:text-[16px] md:text-[18px]">
              <div>
                <span>＊</span>月額費用発生月の直近3ヶ月にご利用がある場合、決済端末1台分のクレジットカード・QRコードの月額費用は値引きされます。
              </div>
              <div>
                <span>＊</span>電子マネーはご利用有無に関わらず毎月月額費用が発生します。
              </div>
              <div>
                <span>＊</span>月額費用は、それぞれの決済種別において、決済事業者の審査（約1‐3ヶ月）後に端末上でご利用できる状態になった時から発生します。
              </div>
              <div>
                <span>＊</span>決済手数料・月額費用には所定の消費税がかかります。
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export default ChokipayInformationMigrate;
