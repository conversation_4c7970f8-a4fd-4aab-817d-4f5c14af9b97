/* eslint-disable no-useless-escape */
import React, { useState, useEffect } from "react";
import { Link, useNavigate } from "react-router-dom";
import { useForm, SubmitHandler } from "react-hook-form";
import icoPagetop01 from "/images/ico_pagetop01.png";
import { useCreateContactMutation } from "@/features/contact/hooks/useCreateContactMutation";

interface FormData {
  agxName: string;
  agxEmail: string;
  agxEmailConfirm: string;
  agxDescriptions: string;
  agxContactContent: string;
  agxBusinessType: string;
  agxPhone: string;
  agxStoreName: string;
  agxCompanyName: string;
}

const mapAgxContactContent = new Map([
  [*********, "チョキペイの申込について"],
  [*********, "チョキペイIDについて"],
  [*********, "チョキペイ専用画面について"],
  [*********, "チョキペイonlineについて"],
  [*********, "その他"],
]);

const mapAgxBusinessType = new Map([
  [*********, "クリニック"],
  [*********, "薬局"],
  [*********, "歯科"],
]);

const Contact: React.FC = () => {
  const navigate = useNavigate();
  const [step, setStep] = useState<"input" | "confirm">("input");
  const [agree, setAgree] = useState(true);
  const [isBacking, setIsBacking] = useState(false);

  const { createContact, isLoading: isSubmitting } = useCreateContactMutation();
  const {
    register,
    handleSubmit,
    formState: { errors },
    watch,
  } = useForm<FormData>({
    defaultValues: {
      agxName: "",
      agxEmail: "",
      agxEmailConfirm: "",
      agxDescriptions: "",
      agxContactContent: "",
      agxBusinessType: "",
      agxPhone: "",
      agxStoreName: "",
      agxCompanyName: "",
    },
  });

  const onSubmit: SubmitHandler<FormData> = async (data) => {
    if (isBacking) return;

    if (step === "input") {
      setStep("confirm");
    } else if (step === "confirm") {
      await createContact(data);
      navigate("/contact-completed", { replace: true });
    }
  };

  const handleBack = () => {
    setIsBacking(true);
    setStep("input");
    setTimeout(() => setIsBacking(false), 0);
  };

  useEffect(() => {
    console.log("useEffect: step updated to:", step);
  }, [step]);

  return (
    <div className="relative mb-[83px]">
      <div className="max-w-[1000px] mx-auto py-6 px-4">
        <div className="text-center">
          <h2 className="text-[37px] font-bold mb-9 text-[#727272]">
            お問い合わせフォーム
          </h2>
          <p className="text-sm font-[600] mb-6 text-[#505050] leading-[2]">
            以下のフォームよりお問い合わせをお願いします。
            <br />
            <Link
              className="text-[#13ae9c] border-b-[1px] border-[#505050]"
              to="https://choqi.co.jp/privacypolicy.html"
            >
              個人情報保護方針
            </Link>
            をご確認・同意の上、必要項目を 入力しお問い合わせください。
          </p>
          <div className="flex justify-center">
            <ul className="flex justify-between gap-2 w-[370px] my-[80px]">
              <li className="flex flex-col items-center text-[15px] text-[#14AEA9] font-[700] whitespace-nowrap gap-2">
                <span
                  className={`w-5 h-5 rounded-full ${
                    step === "input"
                      ? "bg-[#14AEA9]"
                      : "bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px]"
                  }`}
                ></span>
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${
                    step === "input" ? "text-[#14AEA9]" : "text-[#a0a0a0]"
                  }`}
                >
                  入力
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex flex-col text-[15px] text-[#14AEA9] font-[700] items-center whitespace-nowrap gap-2">
                <span
                  className={`w-5 h-5 rounded-full ${
                    step === "confirm"
                      ? "bg-[#14AEA9]"
                      : "bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px]"
                  }`}
                ></span>
                <span
                  className={`w-5 h-5 rounded-full mr-2 ${
                    step === "confirm" ? "text-[#14AEA9]" : "text-[#a0a0a0]"
                  }`}
                >
                  確認
                </span>
              </li>
              <span className="w-full h-0.5 bg-[#a0a0a0] mt-2"></span>
              <li className="flex flex-col items-center text-[15px] text-[#a0a0a0] font-[700] whitespace-nowrap gap-2">
                <span className=" bg-[#a0a0a0] w-[10px] h-[10px] mt-[5px] rounded-full"></span>
                完了
              </li>
            </ul>
          </div>
        </div>

        <form
          onSubmit={handleSubmit(onSubmit)}
          onKeyDown={(e) => {
            if (e.key === "Enter") e.preventDefault();
          }}
          className="bg-[#fafafa] p-6 rounded-lg shadow-sm"
        >
          <table className="w-full space-y-5 md:space-y-0">
            <tbody>
              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お名前{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxName")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxName", {
                          required: "お名前を入力してください。",
                        })}
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    Email{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxEmail")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxEmail", {
                          required: "有効なEmailを入力してください。",
                          pattern: {
                            value:
                              /^[A-Za-z0-9]{1}[A-Za-z0-9_.-]*@{1}[A-Za-z0-9_.-]{1,}\.[A-Za-z0-9]{1,}$/,
                            message: "有効なEmailを入力してください。",
                          },
                        })}
                        type="email"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxEmail && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxEmail.message}
                        </p>
                      )}
                      <div className="mt-3">
                        <p className="text-md font-semibold text-[#333333]">
                          確認用
                        </p>
                        <input
                          {...register("agxEmailConfirm", {
                            required: "確認用Emailを入力してください。",
                            validate: (value) =>
                              value === watch("agxEmail") ||
                              "Emailが一致しません。",
                          })}
                          type="email"
                          className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                        />
                        {errors.agxEmailConfirm && (
                          <p className="mt-1 text-sm text-red-600">
                            {errors.agxEmailConfirm.message}
                          </p>
                        )}
                      </div>
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お電話番号{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxPhone")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxPhone", {
                          required: "有効なお電話番号を入力してください。",
                          pattern: {
                            value: /^[\d\-\(\) ]*$/,
                            message: "有効なお電話番号を入力してください。",
                          },
                        })}
                        type="tel"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxPhone && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxPhone.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    業種{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {mapAgxBusinessType.get(Number(watch("agxBusinessType")))}
                    </span>
                  ) : (
                    <>
                      <select
                        {...register("agxBusinessType", {
                          required: "業種を選択してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      >
                        <option value="">選択してください</option>
                        <option value="*********">クリニック</option>
                        <option value="*********">薬局</option>
                        <option value="*********">歯科</option>
                      </select>
                      {errors.agxBusinessType && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxBusinessType.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お問い合わせ内容{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {mapAgxContactContent.get(
                        Number(watch("agxContactContent"))
                      )}
                    </span>
                  ) : (
                    <>
                      <select
                        {...register("agxContactContent", {
                          required: "お問い合わせ内容を選択してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      >
                        <option value="">選択してください</option>
                        <option value="*********">
                          チョキペイの申込について
                        </option>
                        <option value="*********">チョキペイIDについて</option>
                        <option value="*********">
                          チョキペイ専用画面について
                        </option>
                        <option value="*********">
                          チョキペイonlineについて
                        </option>
                        <option value="*********">その他</option>
                      </select>
                      {errors.agxContactContent && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxContactContent.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    法人名{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxCompanyName") || "未入力"}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxCompanyName")} // Not required as per form
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxCompanyName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxCompanyName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    店舗名{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <span className="block mt-1 text-gray-800">
                      {watch("agxStoreName")}
                    </span>
                  ) : (
                    <>
                      <input
                        {...register("agxStoreName", {
                          required: "店舗名を入力してください。",
                        })}
                        type="text"
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxStoreName && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxStoreName.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <th className="block md:table-cell w-full md:w-1/4 p-0 md:p-5 align-top pt-2 md:pt-[14px]">
                  <label className=" text-[18px] font-medium text-gray-700 flex md:justify-between items-start gap-2 md:gap-0">
                    お問い合わせ詳細{" "}
                    <span className="text-white text-sm  bg-red-500 px-1 py-0.5 whitespace-nowrap  inline-block ml-2 flex-shrink-0">
                      必須
                    </span>
                  </label>
                </th>
                <td className="block md:table-cell w-full md:w-auto pb-4 md:pb-[65px]">
                  {step === "confirm" ? (
                    <pre className="bg-white p-2 mt-1 text-gray-800">
                      {watch("agxDescriptions") || "未入力"}
                    </pre>
                  ) : (
                    <>
                      <textarea
                        {...register("agxDescriptions", {
                          required: "お問い合わせ詳細を入力してください。",
                        })}
                        className="mt-1 block w-full rounded-md border border-gray-300 bg-white p-2 "
                      />
                      {errors.agxDescriptions && (
                        <p className="mt-1 text-sm text-red-600">
                          {errors.agxDescriptions.message}
                        </p>
                      )}
                    </>
                  )}
                </td>
              </tr>

              <tr className="block md:table-row">
                <td colSpan={2} className="block md:table-cell w-full p-0 md:p-5">
                  <div className="flex justify-center w-full flex-col">
                    {step == "input" && (
                      <div className="flex items-center justify-center flex-col mb-4 gap-[25px]">
                        <div
                          className="checkbox"
                          style={{ marginBottom: "5px", marginTop: "0px" }}
                        >
                          <label
                            id="personal_information_lable"
                            style={{ paddingLeft: "3px" }}
                          >
                            <input
                              className="hidden"
                              type="checkbox"
                              name="agree"
                              checked={agree}
                              onChange={() => setAgree(!agree)}
                            />
                            <span>当社の個人情報保護方針に同意する</span>
                          </label>
                        </div>
                      </div>
                    )}
                    <p className="flex items-center justify-center mb-4 text-sm font-semibold text-[#505050]">
                      ※ ｢
                      <a
                        className="text-[#13ae9c] hover:text-[#13ae9c]/80  border-b border-[#505050]"
                        href="https://choqi.co.jp/privacypolicy.html"
                      >
                        個人情報保護方針
                      </a>
                      ｣について別ウインドウで開く
                    </p>
                    <div className="w-full pb-[65px]">
                      {step === "confirm" ? (
                        <div className="flex justify-center gap-4 mt-6">
                          <button
                            type="button"
                            onClick={handleBack}
                            className="px-4 py-2 bg-white border border-gray-300 text-gray-700 rounded-full hover:bg-gray-100 w-[200px] md:w-[240px] flex justify-center items-center"
                          >
                            戻る
                          </button>
                          <div>
                            <ul className="btn01 greenBtn01 centerBtn01">
                              <li
                                style={{
                                  listStyleType: "none",
                                  marginRight: "50%",
                                }}
                              >
                                <button
                                  className="btn01 greenBtn01 centerBtn01"
                                  id="btnNext"
                                  disabled={!agree}
                                >
                                  <span>
                                    {isSubmitting ? "送信中..." : "送信"}
                                  </span>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      ) : (
                        <div className="flex justify-center mt-6">
                          <div>
                            <ul className="btn01 greenBtn01 centerBtn01">
                              <li
                                style={{
                                  listStyleType: "none",
                                  marginRight: "50%",
                                }}
                              >
                                <button
                                  type="submit"
                                  className="btn01 greenBtn01 centerBtn01"
                                  id="btnNext"
                                  disabled={!agree}
                                >
                                  <span>確認画面へ</span>
                                </button>
                              </li>
                            </ul>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>
        </form>
      </div>
      <div>
        <div className="flex justify-end mt-[100px] mr-[20px] mb-5">
          <p
            className=" bg-white rounded-full shadow-lg cursor-pointer"
            onClick={() => {
              window.scrollTo({ top: 0 });
            }}
          >
            <img src={icoPagetop01} alt="pagetop" />
          </p>
        </div>
        <div className="w-full flex justify-center bg-[#EEEEEE] py-4 ">
          <ul className="contactContent01 flex space-x-4 list-none">
            <li className="mailBtn01 text-blue-700">
              <Link to="/contact">メールで相談</Link>
            </li>
            <li className="greenBtn01 btn01 downloadBtn01">
              <Link
                to="/document"
                className="!no-underline hover:text-blue-700 font-semibold flex items-center"
              >
                <p className="z-[999]">資料ダウンロード</p>
              </Link>
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default Contact;
