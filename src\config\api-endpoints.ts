export const API_ENDPOINTS = {
  AUTH: {
    LOGIN: '/auth/signin',
    LOGOUT: '/auth/logout',
    REFRESH_TOKEN: '/auth/refresh-token',
    REGISTER: '/auth/register',
    VERIFY_EMAIL: '/auth/verify-email',
    SIGNUP: '/auth/signup',
    CHANGE_INFO: '/auth/change-info',
    CHANGE_PASSWORD: '/auth/change-password',
  },
  MAIL: {
    REGISTER: '/mail/register',
  },
  APPLICATION: {
    CREATE: "/agx-merchant/user",
    CHANGE_INFO: '/auth/change-info',
    CHANGE_PASSWORD: '/auth/change-password',
  },
  PAYGATE: {
    GET_DATA: (merchantNo: string) => `/notification/paygate?agxMerchantNo=${btoa(merchantNo)}`,
  },
  SUMMARY: {
    GET_DATA: (merchantNo: string) => `/summary?merchantNo=${btoa(merchantNo)}`,
    GET_MONTHLY_DATA: (merchantNo: string) => `/summary-monthly?merchantNo=${btoa(merchantNo)}`,
    GET_CREPICO_DATA: (merchantNo: string) => `/summary/crepico/${merchantNo}`,
  },
  STORE_DEPOSIT: {
    DATES: (agxMerchantNo: string) => `/agx-payment-management/user/date?merchantNo=${btoa(agxMerchantNo)}`,
    DATA: (agxMerchantNo: string, date: string) => `/deposit?agxMerchantNo=${btoa(agxMerchantNo)}&agxPaymentDate=${date}`,
    DETAIL_ALL: (merchantNo: string, paymentDate: string) => `/store-deposit/store-deposit-detail?merchantNo=${btoa(merchantNo)}&paymentDate=${paymentDate}`,
    DETAIL_ELEMENT: (merchantNo: string, paymentBId: string, transactionType: string, datetime: string) => 
      `/agx-payment-detail/store-deposit-detail?merchantNo=${btoa(merchantNo)}&paymentBId=${btoa(paymentBId)}&transactionType=${transactionType}&datetime=${datetime}`,
  },
  ADMIN_DEPOSIT: {
    BASE: 'admin-deposit',
    DETAIL: 'admin-deposit-detail',
    DATES: (agxMerchantNo: string) => `agx-payment-management/user/admin-store/date?merchantNo=${btoa(agxMerchantNo)}`,
    SELECTOR: (agxMerchantNo: string) => `admin-deposit/selector?agxMerchantNo=${btoa(agxMerchantNo)}`,
    DATA: (agxMerchantNoEncode: string, date: string = '', area: string = '', subArea: string = '', merchantNo: string = '') =>
      `admin-deposit?merchantNoAdmin=${agxMerchantNoEncode}&agxPaymentDate=${date}&area=${area}&subArea=${subArea}&merchantNo=${merchantNo}`,
    DETAIL_DATA: (agxMerchantNo: string, transferDate: string, transactionType: string = '', merchantNo: string = '', paymentBId: string = '', area: string = '', subArea: string = '') =>
      `admin-deposit/admin-deposit-detail?merchantNoAdmin=${btoa(agxMerchantNo)}&agxPaymentDate=${transferDate}&transactionType=${transactionType}&merchantNo=${btoa(merchantNo)}&paymentBId=${paymentBId}&area=${area}&subArea=${subArea}`,
  },
  ADMIN_INVOICE_RECEIPT: {
    GET_DATA_INVOICE_RECEIPT: (merchantNo: string) => `admin-invoice-receipt?agxMerchantNo=${merchantNo}`,
    GET_DATA_INVOICE_MONTHLY: (merchantNo: string, yearMonth: string) => `admin-invoice-monthly?agxMerchantNo=${merchantNo}&yearMonth=${yearMonth}`,
    GET_DATA_RECEIPT_MONTHLY: (merchantNo: string, yearMonth: string) => `admin-receipt-monthly?agxMerchantNo=${merchantNo}&yearMonth=${yearMonth}`,
    GET_DATA_CREDIT_CARD_MONTHLY_FEE: (merchantNo: string, yearMonth: string, type: string) => `admin-invoice-monthly/credit-card-monthly-fee?agxMerchantNo=${merchantNo}&yearMonth=${yearMonth}&type=${type}`,
    GET_DATA_MONTHLY_COST_BY_STORE: (merchantNo: string, yearMonth: string) => `admin-invoice-monthly/monthly-cost-by-store?agxMerchantNo=${merchantNo}&yearMonth=${yearMonth}`,
  },
  APPLICATION_STEPS: {
    AGX_MERCHANT_USER_ELEMENT: (merchantNo: string) => `agx-merchant/user/element/application-steps?merchantNo=${merchantNo}`,
    GET_ADDRESS: (zipCode: string) => `ad-address?zip=${zipCode}`,
    UPDATE_APPLICATION_STEPS: (merchantNo: string) => `agx-merchant/user/${merchantNo}?isSendMail=false`,
    UPDATE_MERCHANT: (merchantNo: string, isSendMail: boolean) => `agx-merchant/user/${merchantNo}?isSendMail=${isSendMail}`,
    GET_AGX_FEE_RATE: (agxBusinessType: string) => `agx-fee-rate/user?agxBusinessType=${agxBusinessType}`,
  },
  INVOICE_RECEIPT: {
    AGX_INVOICE_DETAIL: (merchantNo: string) => `agx-invoice-detail/user?agxMerchantNo=${merchantNo}`,
    GET_INVOICE_RECEIPT: (merchantNo: string) => `agx-invoice/user?agxMerchantNo=${merchantNo}`,
    GET_AGX_INVOICE_RECEIPT_DETAIL: (merchantNo: string) => `agx-invoice-detail/user?agxMerchantNo=${merchantNo}`,
    GET_GMO_INFO: (merchantNo: string) => `gmo-member/info?agxMerchantNo=${merchantNo}`,
    GET_GMO_PAYMENT_URL: (merchantNo: string) => `gmo-payment/linkplus-url-payment?agxMerchantNo=${merchantNo}`,
    GET_GMO_LINK_PLUS_URL: (memberId: string, cardNo: string) => `gmo-payment/linkplus-url?memberId=${memberId}&cardNo=${cardNo}`,
    GET_DATA: (invoiceNo: string) => `invoice-receipt/invoice?invoiceNo=${invoiceNo}`,
    GET_DATA_RECEIPT: (invoiceNo: string) => `invoice-receipt/receipt?invoiceNo=${invoiceNo}`,
    GET_DATA_INVOICE_MONTHLY: (merchantNo: string, invoiceNo: string) => `invoice-receipt/invoice-monthly?merchantNo=${merchantNo}&invoiceNo=${invoiceNo}`,
    GET_DATA_RECEIPT_MONTHLY: (merchantNo: string, invoiceNo: string) => `invoice-receipt/receipt-monthly?merchantNo=${merchantNo}&invoiceNo=${invoiceNo}`,
  },
  MERCHANT: {
    AGX_MERCHANT_USER: (agxMerchantNo: string) => `/agx-merchant/user/${btoa(agxMerchantNo)}`
  },
  AGX_FEE_RATE: (agxBusinessType: string | number) => `/agx-fee-rate/user?agxBusinessType=${agxBusinessType}`,
  AREA_SETTING: {
    GET_DATA : (agxMerchantNo: string) => `/area-setting?agxMerchantNo=${btoa(agxMerchantNo)}`,
    DEFAULT:'/area-setting',
    MODIFY: (id: string) => `/area-setting/${id}`
  },
  AGX_SUB_AREA: {
    DEFAULT: "agx-sub-area",
    MODIFY: (id: string) => `/agx-sub-area/${id}`
  },
  AGX_AREA: {
    DEFAULT: "agx-area",
    WITH_ID:  (id: string) => `/agx-area/${id}`
  },
  AGREEMENT: {
    GET_DATA: (merchantNo: string) => `/notification-rules?merchantNo=${merchantNo}`,
  },
  AGX_CREPICO_PAYMENT_DATA: {
    SEARCH: '/agx-crepico-payment-data/search',
  },
  ADMIN_STORE_NEW_TERMINAL_NO: {
    GET_DATA: (merchantNo: string) => `agx-merchant/admin-store/new-terminal-no?merchantNo=${merchantNo}`,
  },
  NOTIFICATION: {
    GET_NOTICE: 'notice/type?type=dashboard',
    GET_NOTIFICATION_APP: (merchantNo: string) => `notification-app?merchantNo=${merchantNo}`,
  }
};
