import React from 'react';
import { LoadingSpinner } from '@/components/LoadingSpinner';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useAdminDeposit } from '../hooks/useAdminDeposit';
import { DepositFilters } from './DepositFilters';
import { DepositTables } from './DepositTables';
import { ExportButtons } from './ExportButtons';
import { formatDateJapan, getPreviousMonthRangeByTransferDate } from '@/utils/dateUtils';

interface AdminDepositPageProps {
  agxMerchantNo: string;
}

export const AdminDepositPage: React.FC<AdminDepositPageProps> = ({ agxMerchantNo }) => {
  const {
    data,
    dates,
    transferDate,
    areaFilters,
    filteredAreas,
    filteredSubAreas,
    filteredMerchants,
    areaSelected,
    isLoading,
    filterLoading,
    error,
    dlEnable,
    handleAreaFiltersChange,
    handleTransferDateChange,
    handleSearch,
    handleExportPDF
  } = useAdminDeposit(agxMerchantNo);



  if (isLoading && !data) {
    return <LoadingSpinner />;
  }

  return (
    <div className="px-4 md:px-2 pt-6 pb-2">
      {/* Header Section */}
      <div className="flex items-center pb-4 gap-8 border-b border-[#6F6F6E] sm:flex-wrap flex-col sm:flex-row md:px-2 lg:px-4">
        {/* Transfer Date Filter */}
        <div className='flex items-center md:gap-10 gap-2 justify-between'>
          <div className="flex items-center gap-2 md:gap-8">
            <Label className="text-2xl text-[#6F6F6E]">振込日</Label>
            <Select value={transferDate} onValueChange={handleTransferDateChange}>
              <div className="relative w-[200px] md:w-[240px]">
                <SelectTrigger className="text-[#6F6F6E] text-xl border-gray-500 border shadow-md [&>span:first-child]:text-center [&>span:first-child]:w-full [&>svg]:hidden">
                  <SelectValue />
                </SelectTrigger>
                <div className="absolute right-3 top-1/2 transform -translate-y-1/2 pointer-events-none">
                  <div className="w-0 h-0 border-l-[6px] border-r-[6px] border-t-[8px] border-l-transparent border-r-transparent border-t-black opacity-50"></div>
                </div>
              </div>
              <SelectContent>
                {dates.map((date, index) => (
                  <SelectItem className="text-[#6F6F6E] text-xl" key={index} value={date}>
                    {formatDateJapan(date)}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {data && (
            <ExportButtons
              data={data}
              transferDate={transferDate}
              dlEnable={dlEnable}
              onExportPDF={handleExportPDF}
            />
          )}
        </div>
        <span className="text-2xl ">
          利用期間｜{
            (() => {
              const { start, end } = getPreviousMonthRangeByTransferDate(transferDate);
              if (!start || !end) return '20xx年mm月dd日〜20xx年mm月dd日';
              return `${formatDateJapan(start)}〜${formatDateJapan(end)}`;
            })()
          }
        </span>
      </div>
      {error && (
        <Alert className="mb-6" variant="destructive">
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {dates.length > 0 ? (
        <div>
          {/* Filters */}
          <DepositFilters
            filters={areaFilters}
            onFiltersChange={handleAreaFiltersChange}
            onSearch={handleSearch}
            filteredAreas={filteredAreas}
            filteredSubAreas={filteredSubAreas}
            filteredMerchants={filteredMerchants}
          />

          {/* Tables */}
          {filterLoading ? (
            <div className="flex justify-center items-center">
              <LoadingSpinner className='min-h-[calc(100vh-511px)]' />
            </div>
          ) : (
            <DepositTables
              data={data}
              areaSelecteds={areaSelected}
              transferDate={transferDate}
              switchLayoutDate="2023-11-05"
            />
          )}
        </div>
      ) : (
        <div className="flex justify-center items-center py-12">
          <h2 className="text-xl text-gray-600">振込データがありません。</h2>
        </div>
      )}
    </div>
  );
};
