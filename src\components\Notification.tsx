import { useGetNotification } from "@/features/overview";
import { TypeStore } from "@/types/globalType";
import exclamationPointIcon from "@/assets/images/グループ 1351.svg";
import { Link } from "react-router-dom";
import { Alert, AlertDescription } from "./ui/alert";

interface NotificationProps {
  isPageOverview?: boolean
}

export const Notification = ({
  isPageOverview = true
}: NotificationProps) => {
  const { noticeData, notificationData, newNotificationData, typeStore, getUrlConfig } = useGetNotification();
  const { title, url, isActive } = getUrlConfig();

  const getUrlReceipt = () => {
    if(typeStore === TypeStore.STORE_PAYGATE) return "/store/invoice-receipt/paygate"
    if(typeStore === TypeStore.STORE_CREPICO) return "/store/invoice-receipt/crepico"
    return "/store/invoice-receipt"
  }
 
  return (
    <>
      <div className="flex items-center gap-2 text-[24px] mb-3">
        <img src={exclamationPointIcon} alt="notification" />
        <h2 className="text-xl md:text-2xl text-[#6F6F6E]">お知らせ</h2>
      </div>
      <Alert className="border border-[#707070] rounded-lg w-full max-w-full xl:max-w-[88%] lg:max-w-[1373px] shadow-md">
        <AlertDescription className="space-y-2 px-6">
          { isPageOverview ? 
            <ul className=" text-[#6F6F6E] list-disc pl-8 text-[18px] md:text-[20px] lg:text-[22px] space-y-3">
              { isActive && (
                <li className="text-[#FF0002] hover:text-[#FF0002]/80">
                  <div className="flex items-center space-x-2 font-medium">
                    <Link 
                      to={url} 
                      className="hover:underline cursor-pointer"
                    >
                      {title}
                    </Link>
                  </div>
                </li>
                )
              }
                
              { (notificationData?.invoiceNotice || newNotificationData?.invoiceNotice) && 
                <li className="text-[#FF0002] hover:text-[#FF0002]/80">
                  <div className="flex items-center space-x-2 font-normal">
                    <Link 
                      to={getUrlReceipt()}
                      className="hover:underline cursor-pointer"
                    >
                      対応が必要な請求があります。
                    </Link>
                  </div>
                </li>
              }

              { (notificationData?.storeDepositNotice || newNotificationData?.storeDepositNotice) && 
                <li>
                  <div className="flex items-center space-x-2">
                    <span>振込情報がアップデートされました。詳しくは振込一覧をご確認ください。</span>
                  </div>
                </li>
              }
                  
              <li>
                <div className="flex items-center space-x-2">
                  <div className="notice-content" dangerouslySetInnerHTML={{ __html: noticeData?.content }} />
                </div>
              </li>
            </ul> :
            <ul className="text-[#6F6F6E] list-disc pl-8 text-[18px] md:text-[20px] lg:text-[22px] space-y-4 py-2">
              { (notificationData?.invoiceNotice || newNotificationData?.invoiceNotice) && 
                <li className="text-[#FF0002] hover:text-[#FF0002]/80">
                  <div className="flex items-center space-x-2 font-normal">
                    <span>ご対応が必要な請求書があります。</span>
                  </div>
                </li>
              }

              <li>
                <div className="flex items-center space-x-2 font-normal">
                  <span>データの保存期間は約5年間となります。必要に応じてデータを保存してください。</span>
                </div>
              </li>
            </ul>
          }
        </AlertDescription>
      </Alert>
    </>
  )
}