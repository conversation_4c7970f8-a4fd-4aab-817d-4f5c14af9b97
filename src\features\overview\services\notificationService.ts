import { API_ENDPOINTS } from "@/config/api-endpoints";
import apiService from "@/services/api";
import { NoticeResponse, NotificationApp, NotificationAppResponse } from "@/features/overview/types";

class NotificationService {
    async getData(): Promise<NoticeResponse> {
        const response = await apiService.get<NoticeResponse>(API_ENDPOINTS.NOTIFICATION.GET_NOTICE);
        return response;
    }

    async getNotificationApp(merchantNo: string): Promise<NotificationApp> {
        const response = await apiService.get<NotificationAppResponse>(API_ENDPOINTS.NOTIFICATION.GET_NOTIFICATION_APP(merchantNo));
        return response.data;
    }
}

export const notificationService = new NotificationService();